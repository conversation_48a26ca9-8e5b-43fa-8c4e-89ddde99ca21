import json
import os
import sys
from typing import Any, Dict, List, Optional, TypedDict, Union

import nest_asyncio
import pandas as pd
from dotenv import load_dotenv
from IPython.display import Image, display
from langchain.output_parsers import PydanticOutputParser
from langchain.prompts import ChatPromptTemplate
from langchain_core.prompts import PromptTemplate
from langchain_core.runnables.graph import MermaidDrawMethod
from langchain_openai import Chat<PERSON>penAI
from langgraph.graph import END, START, StateGraph
from loguru import logger
from pydantic import BaseModel, Field
from sqlalchemy import create_engine, text

# ----------------------- 0. Environment Setup -----------------------
load_dotenv()
nest_asyncio.apply()

logger.remove()
logger.add(
    sys.stdout,
    format="<level>{level}</level>: <cyan>{message}</cyan>",
    colorize=True,
    level="DEBUG",
)
logger.level("DEBUG", color="<blue>")

# ----------------------- 1. Schema -----------------------
class LanguageItem(BaseModel):
    language: str = Field(..., description="The language spoken by the candidate")
    proficiency: Optional[str] = Field(
        None, description="Proficiency level in the language (e.g., B2, C1)"
    )

class ParsedQuery(BaseModel):
    profession: Optional[str] = Field(
        None, description="The profession or job title mentioned"
    )
    skills: Optional[List[str]] = Field(
        None, description="List of skills required for the job"
    )
    experience_years: Optional[Union[str, int]] = Field(
        None,
        description="Years of experience required, e.g., '5+', 'more than 3', '5 plus', 'at least 2 years', 'less than 2 years",
    )
    # education: Optional[str] = Field(None, description="Education requirements")
    languages: Optional[List[LanguageItem]] = Field(
        None, description="List of languages and their proficiency levels"
    )

class GraphState(TypedDict):
    query: str
    parsed: Optional[ParsedQuery]
    retries: int
    sql_query: Optional[str]
    sql_results: Optional[List[Dict[str, Any]]]
    error: Optional[str]


# ----------------------- 2. Database Config -----------------------
db_url = (
    f"mysql+pymysql://{os.getenv('MYSQL_USER')}:{os.getenv('MYSQL_PASSWORD')}"
    f"@{os.getenv('MYSQL_HOST')}:{os.getenv('MYSQL_PORT', 3306)}/candidate"
)
engine = create_engine(db_url)

# ----------------------- 3. LLM Setup -----------------------
api_key = os.getenv("OPENAI_API_KEY")
llm = ChatOpenAI(model="gpt-4.1-mini", temperature=0, api_key=api_key)

parser = PydanticOutputParser(pydantic_object=ParsedQuery)
format_instructions = parser.get_format_instructions()

entity_prompt = ChatPromptTemplate.from_messages(
    [
        (
            "system",
            """You are a recruitment assistant. You will receive natural language queries from employers describing the kind of candidates they are looking for.

Your job is to carefully understand and analyze these queries and extract the following structured information:
- profession: the job title or role the employer is seeking
- skills: a list of specific skills or qualifications mentioned
- experience_years: number of years of experience required. If the query includes expressions like '5+ years', 'more than 3 years', or 'at least 2 years', capture the number and convert it to an integer.
- education: any educational requirements, such as 'bachelor's degree', 'MBA', etc.
- languages: any spoken or written languages mentioned, along with optional proficiency levels (e.g., B2, fluent)

You must ignore irrelevant details and focus only on the fields listed above. If a particular field is not mentioned, leave it as null.
Be strict and precise in your interpretation.

Output your response in the exact format described in the instructions below.
""",
        ),
        ("user", "{query}\n\n{format_instructions}"),
    ]
)

# New skill extraction prompt setup
skill_prompt = ChatPromptTemplate.from_messages(
    [
        (
            "system",
            """You're a skill extraction assistant. You will receive a job title and job description.

Your task is to extract a **list of skills** that a candidate likely has, based on what the job description says — using the job title as context.

Only use the description for identifying real skills. Output a simple Python list of skill names. Don't include explanations.

You need to be very strict, precise in your interpretation and generate the 3 most important skills only based on the job description.
""",
        ),
        ("user", "Title: {title}\nDescription: {description}"),
    ]
)


def parse_json_field(json_str: str) -> Optional[List[Dict[str, Any]]]:
    try:
        # Unescape and parse the JSON string
        return json.loads(json_str)
    except Exception as e:
        logger.error(f"Error parsing JSON field: {e}")
        return None


def extract_skills_for_candidate(candidate: Dict[str, Any]) -> List[str]:
    employment_details = candidate.get("candidate_employment_details", [])
    all_skills = []

    for job in employment_details:
        title = job.get("title", "")
        description = job.get("description", "")
        if not description.strip():
            continue  # Skip empty descriptions

        try:
            formatted_prompt = skill_prompt.format_messages(
                title=title, description=description
            )
            # print(f"the skill prompt is {formatted_prompt}\n\n")
            logger.info(f"Skill prompt: {formatted_prompt}\n\n")
            response = llm.invoke(formatted_prompt)
            # print(f"the response is {response.content}\n\n")
            logger.info(f"Skill response: {response.content}\n\n")
            skills = json.loads(response.content)
            logger.info(f"Skills extracted: {skills}\n\n")
            if isinstance(skills, list):
                all_skills.extend(skills)
        except Exception as e:
            logger.error(f"❌ Skill extraction failed for title='{title}': {e}")

    return list(set(all_skills))  # Remove duplicates


# ----------------------- 4. Graph Nodes -----------------------
def parse_query_node(state: GraphState) -> GraphState:
    query = state["query"]
    retries = state.get("retries", 0)

    try:
        formatted_prompt = entity_prompt.format_messages(
            query=query, format_instructions=format_instructions
        )
        logger.info(f"Entity Prompt: {formatted_prompt}\n\n")
        response = llm.invoke(formatted_prompt)
        logger.info(f"Entity Response: {response.content}\n\n")
        parsed = parser.parse(response.content)
        logger.info(f"Entity Parsed: {parsed}\n\n")

        print(f"\n🔍 Attempt {retries+1} | Parsed:\n{parsed}\n")

        if not any(
            [
                parsed.profession,
                parsed.experience_years,
                parsed.education,
                parsed.languages,
            ]
        ):
            return {**state, "parsed": None, "retries": retries + 1}

        return {**state, "parsed": parsed, "retries": retries}

    except Exception as e:
        print(f"❌ Error parsing: {e}")
        return {**state, "parsed": None, "error": str(e), "retries": retries + 1}


def build_sp_params(parsed: ParsedQuery) -> Dict[str, Any]:
    return {
        "uuid": "df2842bf-f831-47e1-9a75-242ecebcd21e",
        "keyword": parsed.profession or "",
        "years": int(parsed.experience_years or 0),
        "language": parsed.languages[0].language if parsed.languages else "",
        "proficiency": (
            parsed.languages[0].proficiency
            if parsed.languages and parsed.languages[0].proficiency
            else ""
        ),
        "lang_code": "en",
        "limit": 100,
    }


def query_sp_node(state: GraphState) -> GraphState:
    parsed = state.get("parsed")
    logger.info(f"Query SP Node: {parsed}\n")
    if not parsed:
        return state

    params = build_sp_params(parsed)
    logger.info(f"SP Params: {params}\n")

    try:
        with engine.connect() as conn:
            result = conn.execute(
                text(
                    "CALL sp_filter_candidate_for_sureai(:uuid, :keyword, :years, :language, :proficiency, :lang_code, :limit)"
                ),
                params,
            )
            df = pd.DataFrame(result.fetchall(), columns=result.keys())
            return {
                **state,
                "sql_query": str(result),
                "sql_results": df.to_dict(orient="records"),
            }
    except Exception as e:
        print(f"❌ SQL Error: {e}")
        return {**state, "error": str(e)}


def result_node(state: GraphState) -> GraphState:
    results = state.get("sql_results", [])
    parsed = state.get("parsed")
    logger.info(f"Result Node: {results}\n")

    if not results:
        print("⚠️ No results returned from SP.")
        return state

    for r in results:
        for key in [
            "candidate_employment_details",
            "candidate_education_details",
            "candidate_certificate_details",
        ]:
            if key in r and isinstance(r[key], str):
                r[key] = parse_json_field(r[key])

        # 🧠 Skill extraction logic (only if parsed.skills exists)
        if parsed and parsed.skills:
            r["extracted_skills"] = extract_skills_for_candidate(r)
        else:
            r["extracted_skills"] = []

    print(
        f"✅ {len(results)} candidates processed. Skill extraction {'executed' if parsed and parsed.skills else 'skipped'}."
    )
    return {**state, "sql_results": results}


def check_parsing_complete(state: GraphState) -> str:
    if state["parsed"] is not None:
        return "run_sp_query"
    elif state["retries"] >= 3:
        print(
            "⚠️ Please search for a job title, skills, or relevant experience so I can assist you better!"
        )
        return "none"
    else:
        return "retry"


workflow = StateGraph(GraphState)
workflow.add_node("parse_query", parse_query_node)
workflow.add_node("run_sp_query", query_sp_node)
workflow.add_node("show_results", result_node)

workflow.set_entry_point("parse_query")
workflow.add_edge(START, "parse_query")
workflow.add_conditional_edges(
    "parse_query",
    check_parsing_complete,
    {"run_sp_query": "run_sp_query", "retry": "parse_query", "none": END},
)
workflow.add_edge("run_sp_query", "show_results")
workflow.add_edge("show_results", END)

app = workflow.compile()
display(Image(app.get_graph().draw_mermaid_png(draw_method=MermaidDrawMethod.API)))

# ----------------------- 6. Run Example -----------------------
# query = "Find me a registered nurse (RN) or licensed practical nurse (LPN) with 3 years of experience."
# query = "........."  # none will be returned
# query = "Drivers with tanker or bulk cargo experience ready to join a German logistics"
# query = "Find me a nurse"
# query = "Find me a nurse with 2 years of experience who has B1 proficiency in German"

query = "Find me a driver with 0 years of experience"

print(f"\n🚀 Starting search for: '{query}'\n")

initial_state = {
    "query": query,
    "parsed": None,
    "retries": 0,
    "sql_query": None,
    "sql_results": None,
    "error": None,
}

final_state = app.invoke(initial_state)

print("\n📦 Final State:")
# print(json.dumps(final_state, indent=2, default=str))

# ----------------------- 7. Save Results -----------------------

os.makedirs("matched_data", exist_ok=True)

with open("matched_data/matched_data.json", "w") as f:
    json.dump(final_state, f, indent=2, default=str, ensure_ascii=False)

sql_results = final_state.get("sql_results")
if sql_results:
    df = pd.DataFrame(sql_results)
    df.to_csv("matched_data/matched_data.csv", index=False)
    print(
        "✅ Data saved to matched_data/matched_data.json and matched_data/matched_data.csv"
    )
else:
    print("⚠️ No candidates to save.")
