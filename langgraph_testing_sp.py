import json
import os
import sqlite3
import sys
from typing import Any, Dict, List, Optional, TypedDict, Union

import nest_asyncio
from dotenv import load_dotenv
from IPython.display import Image, display
from langchain.output_parsers import PydanticOutputParser
from langchain.prompts import Chat<PERSON>romptTemplate
from langchain_core.runnables.graph import Mermaid<PERSON>rawMethod
from langchain_openai import ChatOpenAI
from langgraph.graph import END, StateGraph, START
from loguru import logger
from pydantic import BaseModel, Field

load_dotenv()

nest_asyncio.apply()


logger.remove()

logger.add(
    sys.stdout,
    format="<level>{level}</level>: <cyan>{message}</cyan>",
    colorize=True,
    level="DEBUG",
)

# Change colors for each level if needed
logger.level("DEBUG", color="<blue>")
logger.level("INFO", color="<green>")
logger.level("WARNING", color="<yellow>")
logger.level("ERROR", color="<red>")
logger.level("CRITICAL", color="<RED><bold>")


# ----------------------- 1. Schema -----------------------


class LanguageItem(BaseModel):
    language: str = Field(..., description="The language spoken by the candidate")
    proficiency: Optional[str] = Field(
        None, description="Proficiency level in the language (e.g., B2, C1)"
    )


class ParsedQuery(BaseModel):
    profession: Optional[str] = Field(
        None, description="The profession or job title mentioned"
    )
    skills: Optional[List[str]] = Field(
        None, description="List of skills required for the job"
    )
    experience_years: Optional[Union[str, int]] = Field(
        None,
        description="Years of experience required, e.g., '5+', 'more than 3', '5 plus', 'at least 2 years', 'less than 2 years",
    )
    education: Optional[str] = Field(None, description="Education requirements")
    languages: Optional[List[LanguageItem]] = Field(
        None, description="List of languages and their proficiency levels"
    )


class GraphState(TypedDict):
    query: str
    parsed: Optional[ParsedQuery]
    retries: int
    sql_query: Optional[str]
    sql_results: Optional[List[Dict[str, Any]]]
    error: Optional[str]


# ----------------------- 2. Database Config -----------------------

DB_PATH = "databases/candidates_details_500.db"


def get_table_schema():
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute("PRAGMA table_info(candidates)")
        schema = cursor.fetchall()
        conn.close()
        return schema
    except Exception as e:
        print(f"Error getting schema: {e}")
        return None


def execute_sql_query(sql_query: str) -> List[Dict[str, Any]]:
    try:
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        cursor.execute(sql_query)
        results = [dict(row) for row in cursor.fetchall()]
        conn.close()
        return results
    except Exception as e:
        print(f"SQL Error: {e}")
        return []


# ----------------------- 3. LLM Config -----------------------

api_key = os.getenv("OPENAI_API_KEY")
llm = ChatOpenAI(model="gpt-4.1-mini", temperature=0, api_key=api_key)

parser = PydanticOutputParser(pydantic_object=ParsedQuery)
format_instructions = parser.get_format_instructions()

prompt = ChatPromptTemplate.from_messages(
    [
        (
            "system",
            """You are a recruitment assistant. You will receive natural language queries from employers describing the kind of candidates they are looking for.

Your job is to carefully understand and analyze these queries and extract the following structured information:
- profession: the job title or role the employer is seeking
- skills: a list of specific skills or qualifications mentioned
- experience_years: number of years of experience required. If the query includes expressions like '5+ years', 'more than 3 years', or 'at least 2 years', capture the number and convert it to an integer.
- education: any educational requirements, such as 'bachelor's degree', 'MBA', etc.
- languages: any spoken or written languages mentioned, along with optional proficiency levels (e.g., B2, fluent)

You must ignore irrelevant details and focus only on the fields listed above. If a particular field is not mentioned, leave it as null.
Be strict and precise in your interpretation.

Output your response in the exact format described in the instructions below.
""",
        ),
        ("user", "{query}\n\n{format_instructions}"),
    ]
)


sql_generation_prompt = ChatPromptTemplate.from_messages(
    [
        (
            "system",
            """You are an expert SQL assistant. Your job is to create a SQLite SQL query to find candidates from the 'candidates' table, 
based on the structured hiring requirements provided. Generate a complete SQL query starting with:

SELECT * FROM candidates WHERE ... 

Schema of the table is below:
{schema}

All string comparisons (such as using LIKE) must be **case-insensitive**, either by using LOWER() on both sides 
or by adding COLLATE NOCASE.

Return only the SQL query, without any explanation or commentary.
""",
        ),
        ("user", "Structured hiring criteria:\n{parsed_data}"),
    ]
)


matching_prompt = ChatPromptTemplate.from_template(
    """
You are a candidate matching engine
You will receive:
    - A natural language search query entered by an employer
    - A list of candidates with structured information (profession, specialization, experience, education, languages, etc.)

Your task is to return only the candidates who match the query exactly or closely, based on clear logical conditions. Additionally, compute a weighted match score for each candidate based on adaptive weight distribution.

    - Employer's Search Query: "{user_query}"
    - Candidate List: {candidate_list}

Your Instructions:
    1. Interpret the search query and extract filters:
        - Profession (e.g., Nurse)
        - Specialization(s) or Skills (e.g., ICU)
        - Minimum years of experience (e.g., > 5 years)
        - Required education level (if specified)
        - Required language(s) and proficiency level (e.g., German B2)
    
    2. For each candidate, check the following:
        - Required profession (or closely related title)
        - Required specialization/skills (e.g., ICU)
        - Required years of experience
        - Required education level (if specified)
        - Required language and minimum proficiency level
        - If language proficiency is slightly higher (e.g., C1 instead of B2), accept it

For candidates missing one or more of the four parameters (skills, experience, education, language), redistribute the weights proportionally among the remaining parameters:
    - Default weights:
        - Skills: 40%
        - Experience: 20%
        - Education: 20%
        - Language: 20%
    - Example redistribution:
        - If experience is missing: Skills 46.67%, Education 26.67%, Language 26.67%
        - If skills is missing: Experience 33.33%, Education 33.33%, Language 33.33%

Calculate Match Score for each candidate using:
    - Match Score = (match_percentage_for_each_parameter * weight_of_that_parameter)
    - Sum all weighted scores to get the final score (e.g., Skill match = 80% * 0.4 = 32)

Return a list of top 25 candidates sorted by Match Score in descending order.
Output only those candidates whose match score is at least 10 (i.e., 10%).
Return the result as a JSON array of matching candidates with their computed match score. Do not explain or add extra text.

Edge Cases to Handle:
    - Ignore candidates with entirely missing data across all parameters
    - Accept equivalent or higher language levels (e.g., B2+ means B2, C1, C2 are valid)
    - Use strict match for specializations
    - If query says "more than 5 years", only include candidates with > 5 years
    - If query says "at least 5 years", include candidates with 5 or more
    - Handle queries that contain multiple specializations or language combinations

Output Format (JSON Only):
[{{
    "name": "Candidate Name", 
    "profession": "Nurse", 
    "experience_years": 6, 
    "skills": ["ICU"], 
    "education": "BSc Nursing", 
    "languages": [{{
        "language": "German", 
        "proficiency": "B2"}}], 
    "match_score": 82.4
}}]
"""
)


# ----------------------- 4. LangGraph Nodes -----------------------


def parse_query_node(state: GraphState) -> GraphState:
    query = state["query"]
    retries = state.get("retries", 0)

    try:
        formatted_prompt = prompt.format_messages(
            query=query, format_instructions=format_instructions
        )
        # Log formatted system and user messages separately
        # for msg in formatted_prompt:
        #     if msg.type == "system":
        #         logger.info(f"SystemMessage:\n{msg.content}\n")
        #     elif msg.type == "human":
        #         logger.info(f"HumanMessage:\n{msg.content}\n")
        response = llm.invoke(formatted_prompt)
        parsed = parser.parse(response.content)

        print(f"🔍 Attempt {retries+1} | Parsed:\n{parsed}\n")

        if not any(
            [
                parsed.profession,
                parsed.skills,
                parsed.experience_years,
                parsed.education,
                parsed.languages,
            ]
        ):
            return {
                "query": query,
                "parsed": None,
                "retries": retries + 1,
                "sql_query": None,
                "sql_results": None,
                "error": None,
            }

        return {
            "query": query,
            "parsed": parsed,
            "retries": retries,
            "sql_query": None,
            "sql_results": None,
            "error": None,
        }

    except Exception as e:
        print(f"Error parsing: {e}")
        return {
            "query": query,
            "parsed": None,
            "retries": retries + 1,
            "sql_query": None,
            "sql_results": None,
            "error": str(e),
        }


def execute_sql_node(state: GraphState) -> GraphState:
    parsed = state["parsed"]

    try:
        schema_info = get_table_schema()
        schema_text = (
            "\n".join([f"{col[1]} ({col[2]})" for col in schema_info])
            if schema_info
            else "id, name, ..."
        )

        formatted_prompt = sql_generation_prompt.format_messages(
            schema=schema_text, parsed_data=parsed.model_dump_json()
        )
        response = llm.invoke(formatted_prompt)
        sql_query = response.content.strip()

        print(f"🧠 LLM-Generated SQL:\n{sql_query}\n")

        results = execute_sql_query(sql_query)
        print(f"✅ Found {len(results)} matching candidates from SQL query\n")

        return {
            "query": state["query"],
            "parsed": parsed,
            "retries": state.get("retries", 0),
            "sql_query": sql_query,
            "sql_results": results,
            "error": None,
        }

    except Exception as e:
        error_msg = f"SQL Error or Generation Failed: {str(e)}"
        print(f"❌ {error_msg}")
        return {
            "query": state["query"],
            "parsed": parsed,
            "retries": state.get("retries", 0),
            "sql_query": None,
            "sql_results": None,
            "error": error_msg,
        }


def match_candidates_node(state: GraphState) -> GraphState:
    query = state["query"]
    raw_candidates = state.get("sql_results", [])

    if not raw_candidates:
        print("⚠️ No candidates to match.")
        return state

    try:
        print(
            f"🤖 Processing {len(raw_candidates)} candidates through matching engine..."
        )

        formatted_prompt = matching_prompt.format(
            user_query=query,
            candidate_list=json.dumps(raw_candidates, ensure_ascii=False),
        )

        response = llm.invoke(formatted_prompt)

        # Debug: Print raw response
        # print(f"🔍 Raw LLM Response (first 200 chars): {response.content[:200]}")

        # Clean the response content
        response_content = response.content.strip()

        # Handle cases where LLM might wrap JSON in markdown code blocks
        if response_content.startswith("```json"):
            response_content = (
                response_content.replace("```json", "").replace("```", "").strip()
            )
        elif response_content.startswith("```"):
            response_content = response_content.replace("```", "").strip()

        # Check if response is empty
        if not response_content:
            print("⚠️ LLM returned empty response. Using original candidates.")
            return {**state, "sql_results": raw_candidates}

        # Try to parse JSON
        try:
            matched_results = json.loads(response_content)
        except json.JSONDecodeError as json_error:
            print(f"❌ JSON Parse Error: {json_error}")
            print(f"🔍 Problematic content: {response_content[:500]}")
            # Fallback: return original candidates
            print("🔄 Falling back to original SQL results...")
            return {**state, "sql_results": raw_candidates}

        # Validate that we got a list
        if not isinstance(matched_results, list):
            print(
                f"⚠️ Expected list, got {type(matched_results)}. Using original candidates."
            )
            return {**state, "sql_results": raw_candidates}

        # print(f"🏆 Matching complete! Found {len(matched_results)} qualified candidates")

        # if matched_results:
        #     print(f"📋 Top {min(3, len(matched_results))} candidates preview:")
        #     for i, candidate in enumerate(matched_results[:3], 1):
        #         name = candidate.get('name', 'Unknown')
        #         score = candidate.get('match_score', 0)
        #         print(f"  {i}. {name} (Score: {score})")
        # else:
        #     print("⚠️ No candidates passed the matching criteria.")

        return {**state, "sql_results": matched_results}

    except Exception as e:
        print(f"❌ Matching Error: {e}")
        print(f"🔄 Falling back to original SQL results...")
        # Return original candidates as fallback
        return {**state, "sql_results": raw_candidates}


def result_node(state: GraphState) -> GraphState:
    """Shows SQL query results before matching"""
    results = state.get("sql_results", [])
    if not results:
        print("⚠️ No SQL results found.")
    else:
        print(f"✅ SQL Query returned {len(results)} candidates")
        # print(f"📋 Sample candidates from SQL:")
        # for i, candidate in enumerate(results[:3], 1):
        #     name = candidate.get("name", "Unknown")
        #     profession = candidate.get("profession", "N/A")
        #     experience = candidate.get("experience_years", "N/A")
        #     print(f"  {i}. {name} - {profession} ({experience} years)")
        # print()
    return state


def final_result_node(state: GraphState) -> GraphState:
    """Shows final matched results after scoring"""
    results = state.get("sql_results", [])
    if not results:
        print("⚠️ No matching candidates found.")
    else:
        print(f"\n📊 FINAL MATCHED RESULTS: {len(results)} candidates")
        # print("=" * 60)
        # # Show top candidates with scores if available
        # for i, candidate in enumerate(results[:5], 1):
        #     name = candidate.get("name", "Unknown")
        #     score = candidate.get("match_score", "N/A")
        #     profession = candidate.get("profession", "N/A")
        #     print(f"  {i}. {name} - {profession} (Score: {score})")
        # print("=" * 60)
    return state


def check_parsing_complete(state: GraphState) -> str:
    if state["parsed"] is not None:
        return "execute_sql"
    elif state["retries"] >= 3:
        print(
            "⚠️ Please search for a job title, skills, or relevant experience so I can assist you better!"
        )
        return "none"
    else:
        return "retry"


# ----------------------- 5. Build LangGraph (Updated Flow) -----------------------

workflow = StateGraph(GraphState)

workflow.add_node("parse_query", parse_query_node)
workflow.add_node("execute_sql", execute_sql_node)
workflow.add_node("sql_result", result_node)
workflow.add_node("match_candidates", match_candidates_node)
workflow.add_node("matching_result", final_result_node)

workflow.set_entry_point("parse_query")
workflow.add_edge(START, "parse_query")

workflow.add_conditional_edges(
    "parse_query",
    check_parsing_complete,
    {"execute_sql": "execute_sql", "retry": "parse_query", "none": END},
)

# Updated workflow: execute_sql -> result -> match_candidates -> final_result -> END
workflow.add_edge("execute_sql", "sql_result")
workflow.add_edge("sql_result", "match_candidates")
workflow.add_edge("match_candidates", "matching_result")
workflow.add_edge("matching_result", END)

app = workflow.compile()

# display(Image(app.get_graph().draw_mermaid_png(draw_method=MermaidDrawMethod.API)))


# ----------------------- 6. Run a Test -----------------------

query = "who is donald trump?"
# query = "who is donald trump" # none will be returned
print(f"🚀 Starting search for: '{query}'\n")

final_state = app.invoke({"query": query, "parsed": None, "retries": 0})

# ----------------------- 7. Print Results in JSON -----------------------

# print("\n" + "=" * 60)
# print("EXECUTION SUMMARY")
# print("=" * 60)
print(f"SQL Query: {final_state['sql_query']}")

results = final_state["sql_results"]

if results:
    print(f"Results Count: {len(results)}")
    print("\n✅ Full Candidate Results in JSON:\n")
    print(json.dumps(results, indent=2, ensure_ascii=False))

    # Optional: save to file
    # with open("matching_candidates.json", "w", encoding="utf-8") as f:
    #     json.dump(results, f, indent=2, ensure_ascii=False)
    #     print("📁 Results saved to 'matching_candidates.json'")
else:
    print("❌ No results found or query failed.")

print("\n🎉 Workflow completed successfully!")
