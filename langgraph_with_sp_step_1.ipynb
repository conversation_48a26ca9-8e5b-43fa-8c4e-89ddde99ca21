{"cells": [{"cell_type": "code", "execution_count": 1, "id": "692ecdea", "metadata": {}, "outputs": [{"data": {"image/png": "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****************************************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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import json\n", "import os\n", "import sys\n", "from typing import Any, Dict, List, Optional, TypedDict, Union\n", "\n", "import nest_asyncio\n", "import pandas as pd\n", "from dotenv import load_dotenv\n", "from IPython.display import Image, display\n", "from langchain.output_parsers import PydanticOutputParser\n", "from langchain.prompts import ChatPromptTemplate\n", "from langchain_core.runnables.graph import MermaidDrawMethod\n", "from langchain_openai import ChatOpenAI\n", "from langgraph.graph import END, START, StateGraph\n", "from loguru import logger\n", "from pydantic import BaseModel, Field\n", "from sqlalchemy import create_engine, text\n", "\n", "# ----------------------- 0. Environment Setup -----------------------\n", "load_dotenv()\n", "nest_asyncio.apply()\n", "\n", "logger.remove()\n", "logger.add(\n", "    sys.stdout,\n", "    format=\"<level>{level}</level>: <cyan>{message}</cyan>\",\n", "    colorize=True,\n", "    level=\"DEBUG\",\n", ")\n", "logger.level(\"DEBUG\", color=\"<blue>\")\n", "\n", "# ----------------------- 1. Schema -----------------------\n", "\n", "\n", "class LanguageItem(BaseModel):\n", "    language: str = Field(..., description=\"The language spoken by the candidate\")\n", "    proficiency: Optional[str] = Field(None, description=\"Proficiency level\")\n", "\n", "\n", "class ParsedQuery(BaseModel):\n", "    profession: Optional[str] = Field(None)\n", "    # skills: Optional[List[str]] = Field(None)\n", "    experience_years: Optional[Union[str, int]] = Field(None)\n", "    education: Optional[str] = Field(None)\n", "    languages: Optional[List[LanguageItem]] = Field(None)\n", "\n", "\n", "class GraphState(TypedDict):\n", "    query: str\n", "    parsed: Optional[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>]\n", "    retries: int\n", "    sql_query: Optional[str]\n", "    sql_results: Optional[List[Dict[str, Any]]]\n", "    error: Optional[str]\n", "\n", "\n", "# ----------------------- 2. Database Config -----------------------\n", "db_url = (\n", "    f\"mysql+pymysql://{os.getenv('MYSQL_USER')}:{os.getenv('MYSQL_PASSWORD')}\"\n", "    f\"@{os.getenv('MYSQL_HOST')}:{os.getenv('MYSQL_PORT', 3306)}/candidate\"\n", ")\n", "engine = create_engine(db_url)\n", "\n", "# ----------------------- 3. LL<PERSON> Setup -----------------------\n", "api_key = os.getenv(\"OPENAI_API_KEY\")\n", "llm = ChatOpenAI(model=\"gpt-4.1-mini\", temperature=0, api_key=api_key)\n", "\n", "parser = PydanticOutputParser(pydantic_object=ParsedQuery)\n", "format_instructions = parser.get_format_instructions()\n", "\n", "prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\n", "            \"system\",\n", "            \"\"\"You are a recruitment assistant. You will receive natural language queries from employers describing the kind of candidates they are looking for.\n", "\n", "Your job is to carefully understand and analyze these queries and extract the following structured information:\n", "- profession: the job title or role the employer is seeking\n", "- skills: a list of specific skills or qualifications mentioned\n", "- experience_years: number of years of experience required. If the query includes expressions like '5+ years', 'more than 3 years', or 'at least 2 years', capture the number and convert it to an integer.\n", "- education: any educational requirements, such as 'bachelor's degree', 'MBA', etc.\n", "- languages: any spoken or written languages mentioned, along with optional proficiency levels (e.g., B2, fluent)\n", "\n", "You must ignore irrelevant details and focus only on the fields listed above. If a particular field is not mentioned, leave it as null.\n", "Be strict and precise in your interpretation.\n", "\n", "Output your response in the exact format described in the instructions below.\n", "\"\"\",\n", "        ),\n", "        (\"user\", \"{query}\\n\\n{format_instructions}\"),\n", "    ]\n", ")\n", "\n", "\n", "# ----------------------- 4. G<PERSON>h <PERSON>des -----------------------\n", "def parse_query_node(state: GraphState) -> GraphState:\n", "    query = state[\"query\"]\n", "    retries = state.get(\"retries\", 0)\n", "\n", "    try:\n", "        formatted_prompt = prompt.format_messages(\n", "            query=query, format_instructions=format_instructions\n", "        )\n", "        response = llm.invoke(formatted_prompt)\n", "        parsed = parser.parse(response.content)\n", "\n", "        print(f\"\\n🔍 Attempt {retries+1} | Parsed:\\n{parsed}\\n\")\n", "\n", "        if not any(\n", "            [\n", "                parsed.profession,\n", "                parsed.experience_years,\n", "                parsed.education,\n", "                parsed.languages,\n", "            ]\n", "        ):\n", "            return {**state, \"parsed\": None, \"retries\": retries + 1}\n", "\n", "        return {**state, \"parsed\": parsed, \"retries\": retries}\n", "\n", "    except Exception as e:\n", "        print(f\"❌ Error parsing: {e}\")\n", "        return {**state, \"parsed\": None, \"error\": str(e), \"retries\": retries + 1}\n", "\n", "\n", "def build_sp_params(parsed: ParsedQuery) -> Dict[str, Any]:\n", "    return {\n", "        \"keyword\": parsed.profession or \"\",\n", "        \"years\": int(parsed.experience_years or 0),\n", "        \"language\": parsed.languages[0].language if parsed.languages else \"\",\n", "        \"proficiency\": (\n", "            parsed.languages[0].proficiency\n", "            if parsed.languages and parsed.languages[0].proficiency\n", "            else \"\"\n", "        ),\n", "        \"lang_code\": \"en\",\n", "    }\n", "\n", "\n", "def query_sp_node(state: GraphState) -> GraphState:\n", "    parsed = state.get(\"parsed\")\n", "    if not parsed:\n", "        return state\n", "\n", "    params = build_sp_params(parsed)\n", "\n", "    try:\n", "        with engine.connect() as conn:\n", "            result = conn.execute(\n", "                text(\n", "                    \"CALL sp_filter_candidate_for_sureai(:keyword, :years, :language, :proficiency, :lang_code)\"\n", "                ),\n", "                params,\n", "            )\n", "            df = pd.DataFrame(result.fetchall(), columns=result.keys())\n", "            return {\n", "                **state,\n", "                \"sql_query\": str(result),\n", "                \"sql_results\": df.to_dict(orient=\"records\"),\n", "            }\n", "    except Exception as e:\n", "        print(f\"❌ SQL Error: {e}\")\n", "        return {**state, \"error\": str(e)}\n", "\n", "\n", "def result_node(state: GraphState) -> GraphState:\n", "    results = state.get(\"sql_results\", [])\n", "    if not results:\n", "        print(\"⚠️ No results returned from SP.\")\n", "    else:\n", "        print(f\"✅ {len(results)} candidates found:\")\n", "        df = pd.DataFrame(results)\n", "        print(df.head())\n", "    return state\n", "\n", "\n", "# ----------------------- 5. G<PERSON>h Setup -----------------------\n", "workflow = StateGraph(GraphState)\n", "workflow.add_node(\"parse_query\", parse_query_node)\n", "workflow.add_node(\"run_sp_query\", query_sp_node)\n", "workflow.add_node(\"show_results\", result_node)\n", "\n", "workflow.set_entry_point(\"parse_query\")\n", "workflow.add_edge(\"parse_query\", \"run_sp_query\")\n", "workflow.add_edge(\"run_sp_query\", \"show_results\")\n", "workflow.add_edge(\"show_results\", END)\n", "\n", "app = workflow.compile()\n", "\n", "display(Image(app.get_graph().draw_mermaid_png(draw_method=MermaidDrawMethod.API)))"]}, {"cell_type": "code", "execution_count": null, "id": "8906b8b5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🚀 Starting search for: 'Find me a nurse with 3 years of experience'\n", "\n", "\n", "🔍 Attempt 1 | Parsed:\n", "profession='nurse' experience_years=3 education=None languages=None\n", "\n", "❌ SQL Error: (pymysql.err.OperationalError) (1318, 'Incorrect number of arguments for PROCEDURE candidate.sp_filter_candidate_for_sureai; expected 6, got 5')\n", "[SQL: CALL sp_filter_candidate_for_sureai(%(keyword)s, %(years)s, %(language)s, %(proficiency)s, %(lang_code)s)]\n", "[parameters: {'keyword': 'nurse', 'years': 3, 'language': '', 'proficiency': '', 'lang_code': 'en'}]\n", "(Background on this error at: https://sqlalche.me/e/14/e3q8)\n", "⚠️ No results returned from SP.\n", "\n", "📦 Final State:\n", "{\n", "  \"query\": \"Find me a nurse with 3 years of experience\",\n", "  \"parsed\": \"profession='nurse' experience_years=3 education=None languages=None\",\n", "  \"retries\": 0,\n", "  \"sql_query\": null,\n", "  \"sql_results\": null,\n", "  \"error\": \"(pymysql.err.OperationalError) (1318, 'Incorrect number of arguments for PROCEDURE candidate.sp_filter_candidate_for_sureai; expected 6, got 5')\\n[SQL: CALL sp_filter_candidate_for_sureai(%(keyword)s, %(years)s, %(language)s, %(proficiency)s, %(lang_code)s)]\\n[parameters: {'keyword': 'nurse', 'years': 3, 'language': '', 'proficiency': '', 'lang_code': 'en'}]\\n(Background on this error at: https://sqlalche.me/e/14/e3q8)\"\n", "}\n"]}], "source": ["# ----------------------- 6. Run Example -----------------------\n", "\n", "query = \"Find me aprofession='nurse' experience_years=3 education=None languages=None\n", " nurse with 3 years of experience\"\n", "# query = \"who is donald trump?\"\n", "\n", "print(f\"\\n🚀 Starting search for: '{query}'\\n\")\n", "None\n", "initial_state = {\n", "    \"query\": query,\n", "    \"parsed\": None,\n", "    \"retries\": 0,\n", "    \"sql_query\": None,\n", "    \"sql_results\": None,\n", "    \"error\": None,\n", "}\n", "\n", "final_state = app.invoke(initial_state)\n", "\n", "print(\"\\n📦 Final State:\")\n", "print(json.dumps(final_state, indent=2, default=str))"]}, {"cell_type": "code", "execution_count": null, "id": "c5650159", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "537bf9d7", "metadata": {}, "source": ["### langgraph with fallback"]}, {"cell_type": "code", "execution_count": 3, "id": "17f55173", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import os\n", "import sys\n", "import json\n", "import pandas as pd\n", "import nest_asyncio\n", "from typing import Any, Dict, List, Optional, TypedDict, Union\n", "from dotenv import load_dotenv\n", "from sqlalchemy import create_engine, text\n", "from pydantic import BaseModel, Field\n", "from langchain.output_parsers import PydanticOutputParser\n", "from langchain.prompts import ChatPromptTemplate\n", "from langchain_openai import ChatOpenAI\n", "from langgraph.graph import END, StateGraph, START\n", "from loguru import logger\n", "\n", "# ----------------------- 0. Environment Setup -----------------------\n", "load_dotenv()\n", "nest_asyncio.apply()\n", "\n", "logger.remove()\n", "logger.add(sys.stdout, format=\"<level>{level}</level>: <cyan>{message}</cyan>\", colorize=True, level=\"DEBUG\")\n", "logger.level(\"DEBUG\", color=\"<blue>\")\n", "\n", "# ----------------------- 1. Schema -----------------------\n", "\n", "class LanguageItem(BaseModel):\n", "    language: str = Field(..., description=\"The language spoken by the candidate\")\n", "    proficiency: Optional[str] = Field(None, description=\"Proficiency level\")\n", "\n", "class ParsedQuery(BaseModel):\n", "    profession: Optional[str] = Field(None)\n", "    # skills: Optional[List[str]] = Field(None)\n", "    experience_years: Optional[Union[str, int]] = Field(None)\n", "    education: Optional[str] = Field(None)\n", "    languages: Optional[List[LanguageItem]] = Field(None)\n", "\n", "class GraphState(TypedDict):\n", "    query: str\n", "    parsed: Optional[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>]\n", "    retries: int\n", "    sql_query: Optional[str]\n", "    sql_results: Optional[List[Dict[str, Any]]]\n", "    error: Optional[str]\n", "\n", "# ----------------------- 2. Database Config -----------------------\n", "db_url = (\n", "    f\"mysql+pymysql://{os.getenv('MYSQL_USER')}:{os.getenv('MYSQL_PASSWORD')}\"\n", "    f\"@{os.getenv('MYSQL_HOST')}:{os.getenv('MYSQL_PORT', 3306)}/candidate\"\n", ")\n", "engine = create_engine(db_url)\n", "\n", "# ----------------------- 3. LL<PERSON> Setup -----------------------\n", "api_key = os.getenv(\"OPENAI_API_KEY\")\n", "llm = ChatOpenAI(model=\"gpt-4.1-mini\", temperature=0, api_key=api_key)\n", "\n", "parser = PydanticOutputParser(pydantic_object=ParsedQuery)\n", "format_instructions = parser.get_format_instructions()\n", "\n", "prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\n", "            \"system\",\n", "            \"\"\"You are a recruitment assistant. You will receive natural language queries from employers describing the kind of candidates they are looking for.\n", "\n", "Your job is to carefully understand and analyze these queries and extract the following structured information:\n", "- profession: the job title or role the employer is seeking\n", "- skills: a list of specific skills or qualifications mentioned\n", "- experience_years: number of years of experience required. If the query includes expressions like '5+ years', 'more than 3 years', or 'at least 2 years', capture the number and convert it to an integer.\n", "- education: any educational requirements, such as 'bachelor's degree', 'MBA', etc.\n", "- languages: any spoken or written languages mentioned, along with optional proficiency levels (e.g., B2, fluent)\n", "\n", "You must ignore irrelevant details and focus only on the fields listed above. If a particular field is not mentioned, leave it as null.\n", "Be strict and precise in your interpretation.\n", "\n", "Output your response in the exact format described in the instructions below.\n", "\"\"\",\n", "        ),\n", "        (\"user\", \"{query}\\n\\n{format_instructions}\"),\n", "    ]\n", ")\n", "\n", "# ----------------------- 4. G<PERSON>h <PERSON>des -----------------------\n", "def parse_query_node(state: GraphState) -> GraphState:\n", "    query = state[\"query\"]\n", "    retries = state.get(\"retries\", 0)\n", "\n", "    try:\n", "        formatted_prompt = prompt.format_messages(query=query, format_instructions=format_instructions)\n", "        response = llm.invoke(formatted_prompt)\n", "        parsed = parser.parse(response.content)\n", "\n", "        print(f\"\\n🔍 Attempt {retries+1} | Parsed:\\n{parsed}\\n\")\n", "\n", "        if not any([parsed.profession, parsed.experience_years, parsed.education, parsed.languages]):\n", "            return {**state, \"parsed\": None, \"retries\": retries + 1}\n", "\n", "        return {**state, \"parsed\": parsed, \"retries\": retries}\n", "\n", "    except Exception as e:\n", "        print(f\"❌ Error parsing: {e}\")\n", "        return {**state, \"parsed\": None, \"error\": str(e), \"retries\": retries + 1}\n", "\n", "def build_sp_params(parsed: ParsedQuery) -> Dict[str, Any]:\n", "    return {\n", "        \"keyword\": parsed.profession or '',\n", "        \"years\": int(parsed.experience_years or 0),\n", "        \"language\": parsed.languages[0].language if parsed.languages else '',\n", "        \"proficiency\": parsed.languages[0].proficiency if parsed.languages and parsed.languages[0].proficiency else '',\n", "        \"lang_code\": 'en',\n", "    }\n", "\n", "def query_sp_node(state: GraphState) -> GraphState:\n", "    parsed = state.get(\"parsed\")\n", "    if not parsed:\n", "        return state\n", "\n", "    params = build_sp_params(parsed)\n", "\n", "    try:\n", "        with engine.connect() as conn:\n", "            result = conn.execute(\n", "                text(\"CALL sp_filter_candidate_for_sureai(:keyword, :years, :language, :proficiency, :lang_code)\"),\n", "                params,\n", "            )\n", "            df = pd.DataFrame(result.fetchall(), columns=result.keys())\n", "            return {**state, \"sql_query\": str(result), \"sql_results\": df.to_dict(orient=\"records\")}\n", "    except Exception as e:\n", "        print(f\"❌ SQL Error: {e}\")\n", "        return {**state, \"error\": str(e)}\n", "\n", "def result_node(state: GraphState) -> GraphState:\n", "    results = state.get(\"sql_results\", [])\n", "    if not results:\n", "        print(\"⚠️ No results returned from SP.\")\n", "    else:\n", "        print(f\"✅ {len(results)} candidates found:\")\n", "        df = pd.DataFrame(results)\n", "        print(df.head())\n", "    return state\n", "\n", "def check_parsing_complete(state: GraphState) -> str:\n", "    if state[\"parsed\"] is not None:\n", "        return \"run_sp_query\"\n", "    elif state[\"retries\"] >= 3:\n", "        print(\"⚠️ Please search for a job title, skills, or relevant experience so I can assist you better!\")\n", "        return \"none\"\n", "    else:\n", "        return \"parse_query\"\n", "\n", "# ----------------------- 5. G<PERSON>h Setup -----------------------\n", "workflow = StateGraph(GraphState)\n", "workflow.add_node(\"parse_query\", parse_query_node)\n", "workflow.add_node(\"run_sp_query\", query_sp_node)\n", "workflow.add_node(\"show_results\", result_node)\n", "\n", "workflow.set_entry_point(\"parse_query\")\n", "workflow.add_edge(START, \"parse_query\")\n", "workflow.add_conditional_edges(\n", "    \"parse_query\",\n", "    check_parsing_complete,\n", "    {\"run_sp_query\": \"run_sp_query\", \"parse_query\": \"parse_query\", \"none\": END},\n", ")\n", "workflow.add_edge(\"run_sp_query\", \"show_results\")\n", "workflow.add_edge(\"show_results\", END)\n", "\n", "app = workflow.compile()\n", "\n", "display(Image(app.get_graph().draw_mermaid_png(draw_method=MermaidDrawMethod.API)))\n"]}, {"cell_type": "code", "execution_count": 5, "id": "7caf53cf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🚀 Starting search for: 'Find me a d with 2 years of experience'\n", "\n", "\n", "🔍 Attempt 1 | Parsed:\n", "profession='d' experience_years=2 education=None languages=None\n", "\n", "❌ SQL Error: (pymysql.err.OperationalError) (1318, 'Incorrect number of arguments for PROCEDURE candidate.sp_filter_candidate_for_sureai; expected 6, got 5')\n", "[SQL: CALL sp_filter_candidate_for_sureai(%(keyword)s, %(years)s, %(language)s, %(proficiency)s, %(lang_code)s)]\n", "[parameters: {'keyword': 'd', 'years': 2, 'language': '', 'proficiency': '', 'lang_code': 'en'}]\n", "(Background on this error at: https://sqlalche.me/e/14/e3q8)\n", "⚠️ No results returned from SP.\n", "\n", "📦 Final State:\n", "{\n", "  \"query\": \"Find me a d with 2 years of experience\",\n", "  \"parsed\": \"profession='d' experience_years=2 education=None languages=None\",\n", "  \"retries\": 0,\n", "  \"sql_query\": null,\n", "  \"sql_results\": null,\n", "  \"error\": \"(pymysql.err.OperationalError) (1318, 'Incorrect number of arguments for PROCEDURE candidate.sp_filter_candidate_for_sureai; expected 6, got 5')\\n[SQL: CALL sp_filter_candidate_for_sureai(%(keyword)s, %(years)s, %(language)s, %(proficiency)s, %(lang_code)s)]\\n[parameters: {'keyword': 'd', 'years': 2, 'language': '', 'proficiency': '', 'lang_code': 'en'}]\\n(Background on this error at: https://sqlalche.me/e/14/e3q8)\"\n", "}\n"]}], "source": ["# ----------------------- 6. Run Example -----------------------\n", "query = \"Find me a d with 2 years of experience\"\n", "# query = \"who is donald trump\" # none will be returned\n", "\n", "print(f\"\\n🚀 Starting search for: '{query}'\\n\")\n", "\n", "initial_state = {\n", "    \"query\": query,\n", "    \"parsed\": None,\n", "    \"retries\": 0,\n", "    \"sql_query\": None,\n", "    \"sql_results\": None,\n", "    \"error\": None,\n", "}\n", "\n", "final_state = app.invoke(initial_state)\n", "\n", "print(\"\\n📦 Final State:\")\n", "print(json.dumps(final_state, indent=2, default=str))\n"]}, {"cell_type": "markdown", "id": "ceffb27d", "metadata": {}, "source": ["### with retry"]}, {"cell_type": "code", "execution_count": 6, "id": "20a49498", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import os\n", "import sys\n", "import json\n", "import pandas as pd\n", "import nest_asyncio\n", "from typing import Any, Dict, List, Optional, TypedDict, Union\n", "from dotenv import load_dotenv\n", "from sqlalchemy import create_engine, text\n", "from pydantic import BaseModel, Field\n", "from langchain.output_parsers import PydanticOutputParser\n", "from langchain.prompts import ChatPromptTemplate\n", "from langchain_openai import ChatOpenAI\n", "from langgraph.graph import END, StateGraph, START\n", "from loguru import logger\n", "from IPython.display import Image, display\n", "from langchain_core.runnables.graph import MermaidDrawMethod\n", "\n", "\n", "# ----------------------- 0. Environment Setup -----------------------\n", "load_dotenv()\n", "nest_asyncio.apply()\n", "\n", "logger.remove()\n", "logger.add(sys.stdout, format=\"<level>{level}</level>: <cyan>{message}</cyan>\", colorize=True, level=\"DEBUG\")\n", "logger.level(\"DEBUG\", color=\"<blue>\")\n", "\n", "# ----------------------- 1. Schema -----------------------\n", "\n", "class LanguageItem(BaseModel):\n", "    language: str = Field(..., description=\"The language spoken by the candidate\")\n", "    proficiency: Optional[str] = Field(None, description=\"Proficiency level\")\n", "\n", "class ParsedQuery(BaseModel):\n", "    profession: Optional[str] = Field(None)\n", "    # skills: Optional[List[str]] = Field(None)\n", "    experience_years: Optional[Union[str, int]] = Field(None)\n", "    education: Optional[str] = Field(None)\n", "    languages: Optional[List[LanguageItem]] = Field(None)\n", "\n", "class GraphState(TypedDict):\n", "    query: str\n", "    parsed: Optional[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>]\n", "    retries: int\n", "    sql_query: Optional[str]\n", "    sql_results: Optional[List[Dict[str, Any]]]\n", "    error: Optional[str]\n", "\n", "# ----------------------- 2. Database Config -----------------------\n", "db_url = (\n", "    f\"mysql+pymysql://{os.getenv('MYSQL_USER')}:{os.getenv('MYSQL_PASSWORD')}\"\n", "    f\"@{os.getenv('MYSQL_HOST')}:{os.getenv('MYSQL_PORT', 3306)}/candidate\"\n", ")\n", "engine = create_engine(db_url)\n", "\n", "# ----------------------- 3. LL<PERSON> Setup -----------------------\n", "api_key = os.getenv(\"OPENAI_API_KEY\")\n", "llm = ChatOpenAI(model=\"gpt-4.1-mini\", temperature=0, api_key=api_key)\n", "\n", "parser = PydanticOutputParser(pydantic_object=ParsedQuery)\n", "format_instructions = parser.get_format_instructions()\n", "\n", "prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\n", "            \"system\",\n", "            \"\"\"You are a recruitment assistant. You will receive natural language queries from employers describing the kind of candidates they are looking for.\n", "\n", "Your job is to carefully understand and analyze these queries and extract the following structured information:\n", "- profession: the job title or role the employer is seeking\n", "- skills: a list of specific skills or qualifications mentioned\n", "- experience_years: number of years of experience required. If the query includes expressions like '5+ years', 'more than 3 years', or 'at least 2 years', capture the number and convert it to an integer.\n", "- education: any educational requirements, such as 'bachelor's degree', 'MBA', etc.\n", "- languages: any spoken or written languages mentioned, along with optional proficiency levels (e.g., B2, fluent)\n", "\n", "You must ignore irrelevant details and focus only on the fields listed above. If a particular field is not mentioned, leave it as null.\n", "Be strict and precise in your interpretation.\n", "\n", "Output your response in the exact format described in the instructions below.\n", "\"\"\",\n", "        ),\n", "        (\"user\", \"{query}\\n\\n{format_instructions}\"),\n", "    ]\n", ")\n", "\n", "# ----------------------- 4. G<PERSON>h <PERSON>des -----------------------\n", "def parse_query_node(state: GraphState) -> GraphState:\n", "    query = state[\"query\"]\n", "    retries = state.get(\"retries\", 0)\n", "\n", "    try:\n", "        formatted_prompt = prompt.format_messages(query=query, format_instructions=format_instructions)\n", "        response = llm.invoke(formatted_prompt)\n", "        parsed = parser.parse(response.content)\n", "\n", "        print(f\"\\n🔍 Attempt {retries+1} | Parsed:\\n{parsed}\\n\")\n", "\n", "        if not any([parsed.profession, parsed.experience_years, parsed.education, parsed.languages]):\n", "            return {**state, \"parsed\": None, \"retries\": retries + 1}\n", "\n", "        return {**state, \"parsed\": parsed, \"retries\": retries}\n", "\n", "    except Exception as e:\n", "        print(f\"❌ Error parsing: {e}\")\n", "        return {**state, \"parsed\": None, \"error\": str(e), \"retries\": retries + 1}\n", "\n", "def build_sp_params(parsed: ParsedQuery) -> Dict[str, Any]:\n", "    return {\n", "        \"keyword\": parsed.profession or '',\n", "        \"years\": int(parsed.experience_years or 0),\n", "        \"language\": parsed.languages[0].language if parsed.languages else '',\n", "        \"proficiency\": parsed.languages[0].proficiency if parsed.languages and parsed.languages[0].proficiency else '',\n", "        \"lang_code\": 'en',\n", "    }\n", "\n", "def query_sp_node(state: GraphState) -> GraphState:\n", "    parsed = state.get(\"parsed\")\n", "    if not parsed:\n", "        return state\n", "\n", "    params = build_sp_params(parsed)\n", "\n", "    try:\n", "        with engine.connect() as conn:\n", "            result = conn.execute(\n", "                text(\"CALL sp_filter_candidate_for_sureai(:keyword, :years, :language, :proficiency, :lang_code)\"),\n", "                params,\n", "            )\n", "            df = pd.DataFrame(result.fetchall(), columns=result.keys())\n", "            return {**state, \"sql_query\": str(result), \"sql_results\": df.to_dict(orient=\"records\")}\n", "    except Exception as e:\n", "        print(f\"❌ SQL Error: {e}\")\n", "        return {**state, \"error\": str(e)}\n", "\n", "\n", "def result_node(state: GraphState) -> GraphState:\n", "    results = state.get(\"sql_results\", [])\n", "    if not results:\n", "        print(\"⚠️ No results returned from SP.\")\n", "    else:\n", "        print(f\"✅ {len(results)} candidates found:\")\n", "        df = pd.DataFrame(results)\n", "        print(df.head())\n", "    return state\n", "\n", "def check_parsing_complete(state: GraphState) -> str:\n", "    if state[\"parsed\"] is not None:\n", "        return \"run_sp_query\"\n", "    elif state[\"retries\"] >= 3:\n", "        print(\"⚠️ Please search for a job title, skills, or relevant experience so I can assist you better!\")\n", "        return \"none\"\n", "    else:\n", "        return \"retry\"\n", "\n", "workflow = StateGraph(GraphState)\n", "workflow.add_node(\"parse_query\", parse_query_node)\n", "workflow.add_node(\"run_sp_query\", query_sp_node)\n", "workflow.add_node(\"show_results\", result_node)\n", "\n", "workflow.set_entry_point(\"parse_query\")\n", "workflow.add_edge(START, \"parse_query\")\n", "workflow.add_conditional_edges(\n", "    \"parse_query\",\n", "    check_parsing_complete,\n", "    {\n", "        \"run_sp_query\": \"run_sp_query\",\n", "        \"retry\": \"parse_query\",\n", "        \"none\": END\n", "    },\n", ")\n", "workflow.add_edge(\"run_sp_query\", \"show_results\")\n", "workflow.add_edge(\"show_results\", END)\n", "\n", "app = workflow.compile()\n", "\n", "display(Image(app.get_graph().draw_mermaid_png(draw_method=MermaidDrawMethod.API)))\n", "\n"]}, {"cell_type": "code", "execution_count": 7, "id": "9e2b542c", "metadata": {}, "outputs": [], "source": ["# # ----------------------- 6. Run Example -----------------------\n", "# query = \"Find me a nurse with 2 years of experience\"\n", "# # query = \"who is donald trump\" # none will be returned\n", "\n", "# print(f\"\\n🚀 Starting search for: '{query}'\\n\")\n", "\n", "# initial_state = {\n", "#     \"query\": query,\n", "#     \"parsed\": None,\n", "#     \"retries\": 0,\n", "#     \"sql_query\": None,\n", "#     \"sql_results\": None,\n", "#     \"error\": None,\n", "# }\n", "\n", "# final_state = app.invoke(initial_state)\n", "\n", "# print(\"\\n📦 Final State:\")\n", "# print(json.dumps(final_state, indent=2, default=str))\n"]}, {"cell_type": "code", "execution_count": null, "id": "4dda9dab", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🚀 Starting search for: 'Find me a nurse with 2 years of experience'\n", "\n"]}], "source": ["# ----------------------- 6. Run Example -----------------------\n", "query = \"Find me a nurse with 2 years of experience\"\n", "# query = \".........\"  # none will be returned\n", "\n", "print(f\"\\n🚀 Starting search for: '{query}'\\n\")\n", "\n", "initial_state = {\n", "    \"query\": query,\n", "    \"parsed\": None,\n", "    \"retries\": 0,\n", "    \"sql_query\": None,\n", "    \"sql_results\": None,\n", "    \"error\": None,\n", "}\n", "\n", "final_state = app.invoke(initial_state)\n", "\n", "print(\"\\n📦 Final State:\")\n", "print(json.dumps(final_state, indent=2, default=str))\n", "\n", "# ----------------------- 7. Save Results -----------------------\n", "\n", "os.makedirs(\"matched_data\", exist_ok=True)\n", "\n", "with open(\"matched_data/matched_data.json\", \"w\") as f:\n", "    json.dump(final_state, f, indent=2, default=str)\n", "\n", "sql_results = final_state.get(\"sql_results\")\n", "if sql_results:\n", "    df = pd.DataFrame(sql_results)\n", "    df.to_csv(\"matched_data/matched_data.csv\", index=False)\n", "    print(\"✅ Data saved to matched_data/matched_data.json and matched_data/matched_data.csv\")\n", "else:\n", "    print(\"⚠️ No candidates to save.\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "c698b164", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "testing", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}