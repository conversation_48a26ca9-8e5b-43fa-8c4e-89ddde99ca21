import os
import sys
import json
import pandas as pd
import nest_asyncio
import streamlit as st
from typing import Any, Dict, List, Optional, TypedDict, Union
from dotenv import load_dotenv
from sqlalchemy import create_engine, text
from pydantic import BaseModel, Field
from langchain.output_parsers import PydanticOutputParser
from langchain.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI
from langgraph.graph import END, StateGraph, START
from loguru import logger
from langchain_core.runnables.graph import MermaidDrawMethod
import io
import base64

# ----------------------- 0. Environment Setup -----------------------
load_dotenv()
nest_asyncio.apply()

# Configure Streamlit page
st.set_page_config(
    page_title="Recruitment Assistant",
    page_icon="🔍",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Configure logger for Streamlit
logger.remove()
logger.add(sys.stdout, format="<level>{level}</level>: <cyan>{message}</cyan>", colorize=True, level="DEBUG")
logger.level("DEBUG", color="<blue>")

# ----------------------- 1. Schema -----------------------

class LanguageItem(BaseModel):
    language: str = Field(..., description="The language spoken by the candidate")
    proficiency: Optional[str] = Field(None, description="Proficiency level")

class ParsedQuery(BaseModel):
    profession: Optional[str] = Field(None)
    experience_years: Optional[Union[str, int]] = Field(None)
    education: Optional[str] = Field(None)
    languages: Optional[List[LanguageItem]] = Field(None)

class GraphState(TypedDict):
    query: str
    parsed: Optional[ParsedQuery]
    retries: int
    sql_query: Optional[str]
    sql_results: Optional[List[Dict[str, Any]]]
    error: Optional[str]

# ----------------------- 2. Database Config -----------------------
@st.cache_resource
def get_database_engine():
    db_url = (
        f"mysql+pymysql://{os.getenv('MYSQL_USER')}:{os.getenv('MYSQL_PASSWORD')}"
        f"@{os.getenv('MYSQL_HOST')}:{os.getenv('MYSQL_PORT', 3306)}/candidate"
    )
    return create_engine(db_url)

# ----------------------- 3. LLM Setup -----------------------
@st.cache_resource
def setup_llm():
    api_key = os.getenv("OPENAI_API_KEY")
    llm = ChatOpenAI(model="gpt-4.1-mini", temperature=0, api_key=api_key)
    
    parser = PydanticOutputParser(pydantic_object=ParsedQuery)
    format_instructions = parser.get_format_instructions()
    
    prompt = ChatPromptTemplate.from_messages(
        [
            (
                "system",
                """You are a recruitment assistant. You will receive natural language queries from employers describing the kind of candidates they are looking for.

Your job is to carefully understand and analyze these queries and extract the following structured information:
- profession: the job title or role the employer is seeking
- skills: a list of specific skills or qualifications mentioned
- experience_years: number of years of experience required. If the query includes expressions like '5+ years', 'more than 3 years', or 'at least 2 years', capture the number and convert it to an integer.
- education: any educational requirements, such as 'bachelor's degree', 'MBA', etc.
- languages: any spoken or written languages mentioned, along with optional proficiency levels (e.g., B2, fluent)

You must ignore irrelevant details and focus only on the fields listed above. If a particular field is not mentioned, leave it as null.
Be strict and precise in your interpretation.

Output your response in the exact format described in the instructions below.
""",
            ),
            ("user", "{query}\n\n{format_instructions}"),
        ]
    )
    
    return llm, parser, prompt, format_instructions

def parse_json_field(json_str: str) -> Optional[List[Dict[str, Any]]]:
    try:
        return json.loads(json_str)
    except Exception as e:
        logger.error(f"Error parsing JSON field: {e}")
        return None

# ----------------------- 4. Graph Nodes -----------------------
def parse_query_node(state: GraphState) -> GraphState:
    query = state["query"]
    retries = state.get("retries", 0)
    
    llm, parser, prompt, format_instructions = setup_llm()

    try:
        formatted_prompt = prompt.format_messages(query=query, format_instructions=format_instructions)
        response = llm.invoke(formatted_prompt)
        parsed = parser.parse(response.content)
        
        st.info(f"🔍 Attempt {retries+1} | Parsed: {parsed}")

        if not any([parsed.profession, parsed.experience_years, parsed.education, parsed.languages]):
            return {**state, "parsed": None, "retries": retries + 1}

        return {**state, "parsed": parsed, "retries": retries}

    except Exception as e:
        st.error(f"❌ Error parsing: {e}")
        return {**state, "parsed": None, "error": str(e), "retries": retries + 1}

def build_sp_params(parsed: ParsedQuery) -> Dict[str, Any]:
    return {
        "uuid": "df2842bf-f831-47e1-9a75-242ecebcd21e",
        "keyword": parsed.profession or '',
        "years": int(parsed.experience_years or 0),
        "language": parsed.languages[0].language if parsed.languages else '',
        "proficiency": parsed.languages[0].proficiency if parsed.languages and parsed.languages[0].proficiency else '',
        "lang_code": 'en',
        "limit": 100
    }

def query_sp_node(state: GraphState) -> GraphState:
    parsed = state.get("parsed")
    if not parsed:
        return state

    params = build_sp_params(parsed)
    st.info(f"📊 SP Params: {params}")

    try:
        engine = get_database_engine()
        with engine.connect() as conn:
            result = conn.execute(
                text("CALL sp_filter_candidate_for_sureai(:uuid, :keyword, :years, :language, :proficiency, :lang_code, :limit)"),
                params,
            )
            df = pd.DataFrame(result.fetchall(), columns=result.keys())
            return {**state, "sql_query": str(result), "sql_results": df.to_dict(orient="records")}
    except Exception as e:
        st.error(f"❌ SQL Error: {e}")
        return {**state, "error": str(e)}

def result_node(state: GraphState) -> GraphState:
    results = state.get("sql_results", [])

    if not results:
        st.warning("⚠️ No results returned from SP.")
    else:
        st.success(f"✅ {len(results)} candidates found:")

        # Clean and convert JSON fields
        for r in results:
            for key in [
                "candidate_employment_details",
                "candidate_education_details",
                "candidate_certificate_details"
            ]:
                if key in r and isinstance(r[key], str):
                    r[key] = parse_json_field(r[key])

        # Display results in Streamlit
        df = pd.DataFrame(results)
        st.dataframe(df, use_container_width=True)

    return {**state, "sql_results": results}

def check_parsing_complete(state: GraphState) -> str:
    if state["parsed"] is not None:
        return "run_sp_query"
    elif state["retries"] >= 3:
        st.warning("⚠️ Please search for a job title, skills, or relevant experience so I can assist you better!")
        return "none"
    else:
        return "retry"

# ----------------------- 5. Workflow Setup -----------------------
@st.cache_resource
def setup_workflow():
    workflow = StateGraph(GraphState)
    workflow.add_node("parse_query", parse_query_node)
    workflow.add_node("run_sp_query", query_sp_node)
    workflow.add_node("show_results", result_node)

    workflow.set_entry_point("parse_query")
    workflow.add_edge(START, "parse_query")
    workflow.add_conditional_edges(
        "parse_query",
        check_parsing_complete,
        {
            "run_sp_query": "run_sp_query",
            "retry": "parse_query",
            "none": END
        },
    )
    workflow.add_edge("run_sp_query", "show_results")
    workflow.add_edge("show_results", END)

    return workflow.compile()

# ----------------------- 6. Streamlit UI -----------------------
def main():
    st.title("🔍 Recruitment Assistant")
    st.markdown("Find candidates based on natural language queries")
    
    # Sidebar for configuration
    with st.sidebar:
        st.header("Configuration")
        
        # Check environment variables
        required_env_vars = ['MYSQL_USER', 'MYSQL_PASSWORD', 'MYSQL_HOST', 'OPENAI_API_KEY']
        missing_vars = [var for var in required_env_vars if not os.getenv(var)]
        
        if missing_vars:
            st.error(f"Missing environment variables: {', '.join(missing_vars)}")
            st.stop()
        else:
            st.success("✅ All environment variables configured")
        
        # Show workflow diagram
        if st.button("Show Workflow Diagram"):
            try:
                app = setup_workflow()
                img_bytes = app.get_graph().draw_mermaid_png(draw_method=MermaidDrawMethod.API)
                st.image(img_bytes, caption="Workflow Diagram")
            except Exception as e:
                st.error(f"Could not generate workflow diagram: {e}")
    
    # Main interface
    st.header("Search for Candidates")
    
    # Example queries
    with st.expander("Example Queries"):
        st.markdown("""
        - "Find me a registered nurse (RN) or licensed practical nurse (LPN) with 3 years of experience."
        - "Drivers with tanker or bulk cargo experience ready to join a German logistics"
        - "Find me a nurse with 2 years of experience who has B1 proficiency in German"
        - "Software engineer with Python experience"
        """)
    
    # Query input
    query = st.text_input(
        "Enter your search query:",
        placeholder="e.g., Find me a nurse with 2 years of experience",
        value="Find me a nurse with 2 years of experience"
    )
    
    if st.button("🚀 Search Candidates", type="primary"):
        if not query.strip():
            st.error("Please enter a search query")
            return
        
        st.markdown(f"**Searching for:** {query}")
        
        # Initialize state
        initial_state = {
            "query": query,
            "parsed": None,
            "retries": 0,
            "sql_query": None,
            "sql_results": None,
            "error": None,
        }
        
        # Run workflow
        with st.spinner("Processing your request..."):
            try:
                app = setup_workflow()
                final_state = app.invoke(initial_state)
                
                # Store results in session state for download
                st.session_state['final_state'] = final_state
                
                # Show final state summary
                with st.expander("Final State Summary"):
                    st.json(final_state)
                
            except Exception as e:
                st.error(f"Error processing request: {e}")
    
    # Download results
    if 'final_state' in st.session_state:
        final_state = st.session_state['final_state']
        sql_results = final_state.get("sql_results")
        
        if sql_results:
            col1, col2 = st.columns(2)
            
            with col1:
                # JSON download
                json_data = json.dumps(final_state, indent=2, default=str, ensure_ascii=False)
                st.download_button(
                    label="📥 Download JSON",
                    data=json_data,
                    file_name="matched_data.json",
                    mime="application/json"
                )
            
            with col2:
                # CSV download
                if sql_results:
                    df = pd.DataFrame(sql_results)
                    csv_data = df.to_csv(index=False)
                    st.download_button(
                        label="📥 Download CSV",
                        data=csv_data,
                        file_name="matched_data.csv",
                        mime="text/csv"
                    )

if __name__ == "__main__":
    main()