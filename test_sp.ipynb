{"cells": [{"cell_type": "code", "execution_count": 1, "id": "faace1c4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-> mysql+mysqlconnector://rahul_ts_qa:nJdnRGG5OKOKmvTekci8rq2YGVdd%5Bj3K5t02qcuTQeUy6a7YgXqjTCbEIbVQHtTv@*************:3306/candidate\n", "Databases on the server:\n", "- candidate\n", "- enterprise\n", "- information_schema\n", "- job\n", "- master_data_management\n", "- mysql\n"]}], "source": ["import os\n", "import urllib.parse\n", "from sqlalchemy import create_engine, text\n", "from sqlalchemy.engine import Engine\n", "from typing import List, Dict, Any\n", "import pandas as pd\n", "\n", "# Load DB credentials from environment\n", "db_host = os.getenv(\"MYSQL_HOST\")\n", "db_port = os.getenv(\"MYSQL_PORT\")\n", "db_user = os.getenv(\"MYSQL_USER\")\n", "db_password = os.getenv(\"MYSQL_PASSWORD\")\n", "db_name = os.getenv(\"CANDIDATE_DATABASE\")\n", "\n", "\n", "# Create database URL\n", "db_url = f\"mysql+mysqlconnector://{db_user}:{urllib.parse.quote_plus(db_password)}@{db_host}:{db_port}/{db_name}\"\n", "print(f\"-> {db_url}\")\n", "\n", "engine = create_engine(db_url, execution_options={\"stream_results\": False})\n", "\n", "with engine.connect() as connection:\n", "    result = connection.execute(text(\"SHOW DATABASES;\"))\n", "    databases = [row[0] for row in result]\n", "    print(\"Databases on the server:\")\n", "    for db in databases:\n", "        print(f\"- {db}\")"]}, {"cell_type": "code", "execution_count": 1, "id": "b1a3bcc3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-> mysql+mysqlconnector://rahul_ts_qa:nJdnRGG5OKOKmvTekci8rq2YGVdd%5Bj3K5t02qcuTQeUy6a7YgXqjTCbEIbVQHtTv@*************:3306/candidate\n", "\n", "📊 Total tables in 'candidate': 37\n", "Tables:\n", "- <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "- candidate_address\n", "- candidate_applied_jobs\n", "- candidate_certificate_detail\n", "- candidate_contact_detail\n", "- candidate_cv\n", "- candidate_cv_history\n", "- candidate_cv_template\n", "- candidate_delete_request\n", "- candidate_document_detail\n", "- candidate_education_detail\n", "- candidate_email_detail\n", "- candidate_employment_detail\n", "- candidate_employment_occupation\n", "- candidate_esco_occupation\n", "- candidate_language_skill\n", "- candidate_master\n", "- candidate_master_history\n", "- candidate_nationality\n", "- candidate_note\n", "- candidate_note_history\n", "- candidate_occupation_category\n", "- candidate_profile_category_map\n", "- candidate_profile_category_master\n", "- candidate_profile_category_master_language_map\n", "- candidate_saved_jobs\n", "- candidate_social_media_detail\n", "- country_vw\n", "- country_vw_v1\n", "- education_course_level_vw\n", "- education_course_level_vw_v1\n", "- language_proficiency_level_vw\n", "- language_proficiency_level_vw_v1\n", "- language_vw\n", "- language_vw_v1\n", "- state_vw\n", "- state_vw_v1\n"]}], "source": ["from sqlalchemy import create_engine, text\n", "import os\n", "import urllib.parse\n", "\n", "# Load DB credentials from environment\n", "db_host = os.getenv(\"MYSQL_HOST\")\n", "db_port = os.getenv(\"MYSQL_PORT\")\n", "db_user = os.getenv(\"MYSQL_USER\")\n", "db_password = os.getenv(\"MYSQL_PASSWORD\")\n", "db_name = os.getenv(\"CANDIDATE_DATABASE\")  # <- explicitly use \"candidate\" here\n", "\n", "# Build DB URL\n", "db_url = f\"mysql+mysqlconnector://{db_user}:{urllib.parse.quote_plus(db_password)}@{db_host}:{db_port}/{db_name}\"\n", "print(f\"-> {db_url}\")\n", "\n", "# Create engine for the \"candidate\" database\n", "engine = create_engine(db_url, execution_options={\"stream_results\": False})\n", "\n", "# Connect and list tables\n", "with engine.connect() as connection:\n", "    result = connection.execute(text(\"SHOW TABLES;\"))\n", "    tables = [row[0] for row in result]\n", "    \n", "    print(f\"\\n📊 Total tables in '{db_name}': {len(tables)}\")\n", "    print(\"Tables:\")\n", "    for table in tables:\n", "        print(f\"- {table}\")\n"]}, {"cell_type": "code", "execution_count": 2, "id": "37a5dd7b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🧩 Stored Procedures in `candidate`:\n", "- delete_candidate (Created: 2025-06-23 05:06:34, Last Altered: 2025-06-23 05:06:34)\n", "- get_candidate_addresses (Created: 2025-06-23 05:06:34, Last Altered: 2025-06-23 05:06:34)\n", "- get_candidate_addresses_v1 (Created: 2025-06-23 05:06:34, Last Altered: 2025-06-23 05:06:34)\n", "- get_employment_detail (Created: 2025-06-23 05:06:34, Last Altered: 2025-06-23 05:06:34)\n", "- get_employment_detail_v1 (Created: 2025-06-23 05:06:35, Last Altered: 2025-06-23 05:06:35)\n", "- get_prefered_occupation_matched_job_count (Created: 2025-06-23 05:06:35, Last Altered: 2025-06-23 05:06:35)\n", "- get_prefered_occupation_matched_job_count_v2 (Created: 2025-06-23 05:06:35, Last Altered: 2025-06-23 05:06:35)\n", "- get_prefered_occupation_matched_job_list (Created: 2025-06-23 05:06:35, Last Altered: 2025-06-23 05:06:35)\n", "- get_prefered_occupation_matched_job_list_v2 (Created: 2025-06-23 05:06:35, Last Altered: 2025-06-23 05:06:35)\n", "- get_sync_candidate_records (Created: 2025-07-01 05:56:30, Last Altered: 2025-07-01 05:56:30)\n", "- sp_get_candidate_job_interest_program_panel_member (Created: 2025-06-23 05:06:36, Last Altered: 2025-06-23 05:06:36)\n", "- sp_get_candidate_note_list (Created: 2025-06-23 05:06:36, Last Altered: 2025-06-23 05:06:36)\n", "- sp_get_candidate_note_list_total_record (Created: 2025-06-23 05:06:36, Last Altered: 2025-06-23 05:06:36)\n", "- sp_get_candidate_sourcing_partner_detail (Created: 2025-06-23 05:06:36, Last Altered: 2025-06-23 05:06:36)\n", "- sp_get_sourcing_partner_program_detail_for_show_interest (Created: 2025-06-23 05:06:36, Last Altered: 2025-06-23 05:06:36)\n", "- v1_get_candidate_details_for_cv_generate (Created: 2025-06-23 05:06:37, Last Altered: 2025-06-23 05:06:37)\n", "- v1_get_prefered_occupation_matched_job_count (Created: 2025-06-23 05:06:37, Last Altered: 2025-06-23 05:06:37)\n", "- v1_get_prefered_occupation_matched_job_list (Created: 2025-06-23 05:06:37, Last Altered: 2025-06-23 05:06:37)\n", "- v2_get_candidate_details_for_cv_generate (Created: 2025-06-23 05:06:37, Last Altered: 2025-06-23 05:06:37)\n"]}], "source": ["# Query to get stored procedures from information_schema.ROUTINES\n", "query = \"\"\"\n", "SELECT ROUTINE_NAME, ROUTINE_TYPE, CREATED, LAST_ALTERED\n", "FROM information_schema.ROUTINES\n", "WHERE ROUTINE_SCHEMA = :db_name\n", "AND ROUTINE_TYPE = 'PROCEDURE';\n", "\"\"\"\n", "\n", "# Execute and print results\n", "with engine.connect() as connection:\n", "    result = connection.execute(text(query), {\"db_name\": db_name})\n", "    procedures = result.fetchall()\n", "\n", "    if procedures:\n", "        print(f\"\\n🧩 Stored Procedures in `{db_name}`:\")\n", "        for proc in procedures:\n", "            print(f\"- {proc.ROUTINE_NAME} (Created: {proc.CREATED}, Last Altered: {proc.LAST_ALTERED})\")\n", "    else:\n", "        print(f\"\\n⚠️ No stored procedures found in `{db_name}`.\")"]}, {"cell_type": "code", "execution_count": null, "id": "88a7b12d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>uuid</th>\n", "      <th>name</th>\n", "      <th>profile_photo</th>\n", "      <th>country_name</th>\n", "      <th>city_name</th>\n", "      <th>state_name</th>\n", "      <th>total_experience_years</th>\n", "      <th>candidate_employment_details</th>\n", "      <th>candidate_education_details</th>\n", "      <th>candidate_certificate_details</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>c73871b1-e229-4e1a-9ecc-4ab65eb80e89</td>\n", "      <td><PERSON></td>\n", "      <td>None</td>\n", "      <td>India</td>\n", "      <td>Chennai</td>\n", "      <td>Tamil Nadu</td>\n", "      <td>44</td>\n", "      <td>[{\"title\": \"Off-Spinner\", \"company_name\": \"Ind...</td>\n", "      <td>[{\"title\": \"BTech\", \"description\": \"\", \"instit...</td>\n", "      <td>[{\"title\": \"ICC Best Off Spinner of the Decade...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                   uuid      name profile_photo country_name  \\\n", "0  c73871b1-e229-4e1a-9ecc-4ab65eb80e89  R Ashwin          None        India   \n", "\n", "  city_name  state_name total_experience_years  \\\n", "0   Chennai  Tamil Nadu                     44   \n", "\n", "                        candidate_employment_details  \\\n", "0  [{\"title\": \"Off-Spinner\", \"company_name\": \"Ind...   \n", "\n", "                         candidate_education_details  \\\n", "0  [{\"title\": \"BTech\", \"description\": \"\", \"instit...   \n", "\n", "                       candidate_certificate_details  \n", "0  [{\"title\": \"ICC Best Off Spinner of the Decade...  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "import pandas as pd\n", "from sqlalchemy import create_engine, text\n", "\n", "# Create DB connection string\n", "db_url = (\n", "    f\"mysql+pymysql://{os.getenv('MYSQL_USER')}:{os.getenv('MYSQL_PASSWORD')}\"\n", "    f\"@{os.getenv('MYSQL_HOST')}:{os.getenv('MYSQL_PORT', 3306)}/candidate\"\n", ")\n", "\n", "engine = create_engine(db_url)\n", "\n", "# Calling stored procedure\n", "params = {\n", "    \"uuid\": \"df2842bf-f831-47e1-9a75-242ecebcd21e\", # dynamic\n", "    \"keyword\": \"d\",\n", "    \"years\": 2,\n", "    \"language\": \"german\",\n", "    \"proficiency\": \"B1\",\n", "    \"lang_code\": \"en\", # will be get from user, dynamic\n", "}\n", "\n", "with engine.connect() as conn:\n", "    result = conn.execute(\n", "        text(\n", "            \"CALL sp_filter_candidate_for_sureai(:uuid, :keyword, :years, :language, :proficiency, :lang_code)\"\n", "        ),\n", "        params,\n", "    )\n", "    df = pd.DataFrame(result.fetchall(), columns=result.keys())\n", "\n", "# Display top 5\n", "df"]}, {"cell_type": "code", "execution_count": null, "id": "fe72e2d0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c88d7dcb", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "852a6956", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "03aa74a8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c278047d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3354edd1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0c853c18", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7ae13f51", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 1, "id": "43cb9d7e", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAJAAAAGwCAIAAADJ/3ttAAAAAXNSR0IArs4c6QAAIABJREFUeJztnXlYE0f/wCfJJuQgIUA45L4EFFBQFEWrIocKqCB4IV6tB/rWn+hrD2utoj0UtbbWqrXWWuuBB2q971u0niCgqIicch8Jucj5+yO+qbUQgu4mTDqfx8cn2Zmd+W4+7O7s7uwMSa1WAwQ8kI0dAKJjIGGQgYRBBhIGGUgYZCBhkIEZq+L6yhYRXyluVkjFKplUZaww9IdEAhiNxGJjTA6FY0XlWFONE4aBr8PKC8UvckVFeSJ7V7pUrGSyMQsjbXmHIQGZRCVqVogFSgoGRAKlux/LswfLxolu0CgMJqyyWJJ1rJ5rQ7XuYubhzzLWXyhe1FW0vMgXNdXIFHJ16Ciewf7sDCTs8sGaunJZ6EhrB0+GAaozJIU5wqyjdT7B7JAR1gaojnBh4mbF3vSyyGQ7Fx8moRUZl4I7grwsQeJ8J8JrUhOJVKzY9nmRSCAntJZOwssi8ZZPClUqFaG1ECiMXy/75Ysi4srvhAj58s0fFRJaBYHXYXvTS5MXuxJXfieExcFGz3U4+F05cVUQdQ47t6e6x0ALOxeDNnk7CU/uChpr5P2iCWmDELKHFeYIFS2qf6ctAIBPMKcwW9hYIyOicEKEZR2rCx3JI6JkWAgdaZ11rJ6IkvEX9uSuwKc324IH93XxO+IRYG7GIFcVS3AvGX9hT+8L7Vz/pQfD17Gypz1/KMK9WJyFqVTq0gKxW3cWvsXq5vnz57GxsW+x4v79+5ctW0ZARAAA4O7HepHf6YWVPBL59efgW2a7PHr0yMAr6oOlHY1jheHe9MD58UpjjZxqRtS1XXNz85YtW65fv97Q0NC9e/cRI0bExcVt2bJl27ZtAIDg4OAFCxZMmjTp2rVrZ86cefDgAZ/P9/f3nzFjRnBwMACgsLBwwoQJ33333Zdffmlpaclms+/fvw8AOHHixK5du3x9fXEPmEQi8evklrY0HMvEWZiIr2BbEdXcSEtLq66uXrx4sbu7+/79+7/55hsPD4+UlBSZTHb27Nnjx48DAKRS6eeff963b9+0tDQAwPnz5xcsWHDkyBFra2sqlQoA2LZt2+TJkwMDA/38/KZNm+bq6qrJSQRMDkUsUOJbJt7CBEp7N6JaHPfv358yZUq/fv0AAPPmzYuIiOByuW/kodPpGRkZDAZDk+Tv73/w4MHs7Ozw8HASiQQA6Nev36RJkwiK8A1YHEwkUOBbJs7CyBRAoZLwLVNLYGDgrl27mpqaevXq1b9//27durWaTSQSbdy48d69e3V1dZoljY2N2tS21iICKo2kwvtZOs7nGzM6RdiE89+UluXLlyclJd28eXPhwoWRkZGbN29WKN6sq6qqasaMGXK5/Ouvv7558+atW7fejNDMjKDw/omgUcFgUfAtE+c9jGVBEfFxPmpr4XA477///vTp03Nyci5duvTLL7+w2ezk5OTX85w7d04mk6WlpTEYjDf2LcMjFiiZvjgLw3kPs+BRCbqbzOfz9+3bJ5VKSSRSYGDgggULgoODCwoK/pmNw+FobAEALly4QEQwekI1I7Et8T7p4Fuciy8z74YA3zI1YBi2devWTz75JCcnp76+/sSJEwUFBYGBgQAAFxeXurq6y5cvl5SUdO3ata6uLjMzU6FQZGVl3b59m8vlVlVVtVqms7NzXl7enTt3GhoacA9Y2KSoKJTg3kWHsnz5chyLw6jk4kcirg2VbYlz455GowUEBJw7d+7XX3/dtWtXWVnZzJkz4+LiSCQSj8d79OjRjh07uFzu+PHjlUrlnj17NmzY0NjYuGTJErFY/Pvvv9fV1fXo0WPfvn3R0dFOTq8e5FtaWl67dm3v3r0hISHahXjx9F4zjUF298P5pg/+z8MeXm+Sy9S9h1riWyx0XD5Q4xHAcvHFWRj+dyV6DOTeOdMgb4GgbyhxVJVIa8tbcLdF1BPnh9ebGqvkgxNtWk29fPlyW8dhCwsLPp/falJcXFxqaiquYf5FampqdnZ2R0NKS0sbPHhwq0mHNpaHDLd29MK/Tx9RXQSO//wybJwNy6KVM5lCoZBIWn9QJJfLNTeQ/gmVSqXTibqHIhaLlcrWr0Z0hMRgMDCslUZg+VPxsxxh2FhbvMMEgLhubiKBfNvn/64uUxrEzYqflzwnrnyi7qwz2VjUZLuDGwjsP9Q52bO6ZOLHLsSVT2zP3/qqlkv7ag3RH7YT0CJR7llVOvFTFzoD57sbr0Ps+2HW9mb9oq22fV7U3CgntCKjU1Us+W1FSWKqE6G2DPQyhESovJBRzWRjoSOt6Uxit8fwNFbLbhyrY7Ao4RPtDFCd4V43yr/JzzpW33Mwt4s73dkb+hcjVCr1izxRTan0ea5owEieu7+BurEY+oW+/Jv8Zw+EVcVS/4EWQA1YFhS2JZVMIeoRGr7IpEoRXykSKBQy1eM/m939WV5B5t5BbEPGYGhhGuQyVWmBWFAvF/GVshaVRIjzE5nS0lI6nW5ri+eVEIUCKBiZZUFhcTCuLdW1m0F7hmkxjjCiSU9Pd3V1HT9+vLEDwR80igBkIGGQgYRBBhIGGUgYZCBhkIGEQQYSBhlIGGQgYZCBhEEGEgYZSBhkIGGQgYRBBhIGGUgYZCBhkIGEQQYSBhlIGGQgYZCBhEGGaQpjMpk0Gp5DcnUeTFOYWCyWyQgZctfomKYwEwYJgwwkDDKQMMhAwiADCYMMJAwykDDIQMIgAwmDDCQMMpAwyEDCIAMJgwwkDDJMamCV0aNHq9VqpVIpEAhoNBqLxVIqlWQy+dixY8YODTdwHgbfuPB4vPv371MoFACARCLh8/lqtToiIsLYceGJSR0SJ02aZGVl9foSa2vr6dOnGy8i/DEpYUOHDnVzc3t9SWBgIBEzuRkRkxIGAEhKSmIyXw3GaG9v/8EHHxg7IpwxNWHh4eGenp6azz179vTx8TF2RDhjasIAABMnTmSxWHZ2dpMnTzZ2LPjT4VYiv07eWCPDfeI5HPFyGODnPpTH49EUzkV5+E/MixcYRrLuQmNZdExBB67Dyp6K711o5NfJnb1ZxE3D9++BaYGVPhLaOJsNGmNjYa3vZFD6Cqt8IblyqC5qsiNxs/7+OxE0yC5mVMalOOg5gZdev37dy5YLGTUxM5yRLdzhWNHi5rr+trJErdJrz9FLwN2zjaGjiJn6BQEAAGBgnO2tk/X65NRLWOkTsQXPNN8t6CSYW1Irnkv1ydm+MKlYxbbEaHRTm9GhU8GxourZ+GtfGJkMmhtRm5BY1CogbNJrehrUiIAMJAwykDDIQMIgAwmDDCQMMpAwyEDCIAMJgwwkDDKQMMhAwiADCYMMJAwyCBG2/8CuuDER169fHpMYNTSiT/KU+LNnT2iShELhrzu2zPnP1BExA5Mnx23avF4qffXgbtnyj1esXPzT1g1h4cFXr11Uq9UHM/fMnJU0PHrA7JTkn7dtVCpfzR6cn//w408+HDU6bPLUMZs2rxeJ9OoateWn78ckRoWFB69Zu/LWreth4cH19XUAgMVLUhcvSdVmO3PmeFh4sFgs1nw9febY3A+njYgZOPfDaQcz92i7wIyOD8/M3Dt/wcyw8ODrNy6HhQfn5eVoCyksfBoWHvyysgKnX/QvCBFGoWAikfDCxdO7f//jyOEL4UOHrUpfXlZWAgA4dDhjz94d48dN/vqr72bPnn/5yrnfdm7VrEWlUoteFBa9KPxq5bc9AoIOHcrYtXt7YkJSxp7jI0cmnDh5JGPfTgBAeUXZoo/nSlukG3/4dWXa2qKiZwsWzlIo2nlid/zE4YOZe1Lnf/rHkYvduwf88ONaAACGtdPF7PyF06vT07y7+u7ZdXTGB/85mLln46Z12miPnzzs5eWzJv3HfiED7ezsz184pV3xytXzFhbcLvYO7/xbvglRh0SFQjEmfgKDweCwOdOmzmYxWRcungEAjBubvG3r3iGDI4ICg98bGBY2JOr2nSzNKiQSqarqZdqy9NDQQVyuZc7D+z4+3YcNi+VyLWNj4n/cuCOk7wAAwPnzp6gYdWXaWhcXNzc3j0X/Xfqs8Mn1G5d1x3Pq9NH3BoYNem8oh82JiY4L7Nlbn604efJIjx5BqfM/tbS06hXUZ/rUlCNH9jc2Nmii5XAs5v1nUXDvEAzDRsYmXLx4RnsMuHT53LCoWBIJ/xngCTyHeXt303wgkUgODk6lpS80f5h37t6cM3dK5LB+YeHB+w/s0my/BlcXdzqdrvns79/z3r0/09esOH3mGF/Ad3Rw8vLyBgDk5+f4+vpZWHA12eztuzg4OD3MfaA7mMLCJz4+****************************************+Xj/VWBMdJxQJPzzzxsAgKKiwoqKsugRozvya+kLge+HmZmZ/fWZTheJhACArT//cPLkkdmz5/cJ7m9nZ7/tlx9PnvpDm4322iqJCUlMJutG1pXV6WkYhg0ZEjl75v/xeDZCYXPBk0dh4cGv19XYoKvLkUgkkslkDAZTu4ROZ7Qbv0wmk8vlv2zf9Mv2TX+r639/Ya8PesrlWg4IHXzh4unQ0EFXrp737urr6urebhVvAYHCRCIRi8XSfG6RSi25Vmq1+tjxzMSEpNiYeM1yobC5rdXJZHJsTHxsTHxxcdH9+7d37NwqEgm//nK9lTUvICBw+rSU1zNbcLg6ImEymRQKpaXlr25JEom4rcxK1avDGp1OZzKZUZExgwaFv57BoYtTqyvGRMelrfxU0Cy4fuNy9Ig4HfG8CwQKe5B9Z+CAIQCAlpaW0rLi/v3fk8vlEomEx3vVxVEmk2XdvNrW6mfOHPf27ubu7unm5uHm5tEsbD5x8jAAwNOj69lzJ3r26EUmvzqeFxcXOTm56IiERCLZ2zs8efJIu+T1QyiNSmviN2q/ahpHGjw9vZuFzUGBr/ZmuVxeWVlha2vXai0hIQM4HIt9+3aWlLyICB+uxy/0NhB1DiOTyYcOZZSWFiuVyu2/bm5paQkfOpxGo7m4uJ06fbTiZTmf35S+dkWAf2Bzs6DVdvmFi6e/WP5RVtZVvoB/69b1a9cv+vv1BAAkJk5SqVQbN62TSqVlZSU/bd3w/ozxRS8KdcczZHDExUtnr1y9IBaLDx3ed/t2ljapWzf/goL8oqJCAMDde3++3n6Z+cGHN25cPnnqD5VKlZubvWLl4oWLUtoaTZhEIo0YPirz0N7Q/oO0p1jcIWoPI5FI48YmL1yUUl9fx2AwPv14ubOzKwBg6ZKvf9y0btr0RDqdPnfOwsDA4Nu3s+ITIn7bkflGCf9d+PnGH9cuWboQAGBlZR0bEz82MRkAwGFzftm2LyPjt9lzkktLi319/T5atNS7azuvWSZP+qC+vu77DasbGxs8PLySJ73/46ZvNUlxo8eVlhbPSpmkVCqHhkUlJ72/Kn25pj0SEBC4dcvu3Xt+/WnrBqlU4te9x5crv3393PwGoaGDf9v5c1RkDB4/Yeu0/zKETKrakVY88VMP/QvNPJSxafO3F87dfufwiOLS5XMrVi4+nHmOy7XEsdiMfTuPHj246/cj2sO1nogFipO/lE1f3n47xaRGETAi2dn3XlaW/7Zz6/Jl6R211SFMR9jiJal5udmtJkVHx81JSW01CS8+/vRDCoXywftzQ/qGEloRIYdEo1BfXyeTt94cYDKYxLUCcOHfeEi0tuYZOwRDgB6vQAYSBhlIGGQgYZCBhEEGEgYZSBhkIGGQgYRBRvvCSGQSr0ubDxQQuKBSAp6DXj9y+8KoNJKoWcGvN805QDsJ9VVSMkWvLlZ6HRK7BpnXlEjeOSpEm9S/lHoEsPTJqZewftHWT+7wKwo779iDUPPwWoNUpOzWl6NPZn2H31Op1PvWlnn0YLOtaJb2ZsB0Brs3Gmq1uq5C2ljdIhUqhk2x13Otjk00kHO1qfSJGKhJDZUtbxunIZArFCQSCaN06vGxrB3MKFSSuz/TN1ivfUuDSc0MoSU9Pd3V1XX8+PHGDgR/0HUYZCBhkIGEQQYSBhlIGGQgYZCBhEEGEgYZSBhkIGGQgYRBBhIGGUgYZCBhkIGEQQYSBhlIGGQgYZCBhEEGEgYZSBhkIGGQgYRBhmkK43K5DEb7Q1jCiGkKa2pqkkhM8+0N0xRmwiBhkIGEQQYSBhlIGGQgYZCBhEEGEgYZSBhkIGGQgYRBBhIGGUgYZCBhkIGEQYZJDawyfvx4DMNUKlVtba2ZmRmXy1WpVCqVat++fcYODTdMZ2YIDU+ePNF+rq6uViqVQUFBRo0IZ0zqkJiQkEClUl9fYmlp+cEHHxgvIvwxKWFjxoxxd//bfDM+Pj4DBgwwXkT4Y1LCMAyLi4vTzqDH4XCmTZtm7KBwxqSEAQDi4+OdnZ01n319fUNCQowdEc6YmjAqlZqYmGhmZsZms6dMmWLscPBHr1aiQq6SCFXEB4MPkWGjMvedtLOz8/MJbm5UGDscvVCrAMdaLxftXIc9vi14eI3fUCVjmnfq0T1hh8Gh1JS2uPgyew3lOnVl6sipS9jtsw11L+WBg63YVtS28iBwhF8nu3msptdQrmcP87bytCnsz9MNgnpFv1hbIiNEtMK53yt6DLTwCmzdWeuNjsYaWV1FC7JlFCKSHXKuNbWV2rqwuooWtVqviQoQuEMikaRCVX0b45a3LkzIV9o40wkODNEmjl7Mphp5q0mtNyXlLSq5lOCgEG0jalaolK0nmdqFs8mDhEEGEgYZSBhkIGGQgYRBBhIGGUgYZCBhkIGEQQYSBhlIGGQgYZCBhEEGbsJGx4dnZu6dv2BmWHiwoFmweEnq4iWp2tQzZ46HhQeLxWIAQNqKT1esXJyVdXVU3NDIYf3mL5j5+HFeu+WXlhanrfg0PiEybkzEkqULc3OzNctjRw3es3fHsuUfh4UHx44avHhJarOwud3SiouLUuZMjogKSRw3/OHDB/Pmf7Du268AAI8L8sPCgx8X5GtzJk+O27R5veZzfv7Djz/5cNTosMlTx2zavF4kejUTeeahjISxw67fuBwe2Xf996tGxAzctXu7tgSlUjkqbuhPWzd05OdsE9yEUanU4ycPe3n5rEn/kcnQ1e8Hw7D8Rw/PnT+5ZfPvp05cN6OZfbN6me7CZTJZ6sJZFApl9aof1q3ZjFGwJZ8vkEqlAAAKBTtwcHds7JiL5++kr9pYWlr8w8Y1uktTKpWfLJ5naWW9d/ex9FUbM/bvLCsreaNT/j8pryhb9PFcaYt04w+/rkxbW1T0bMHCWQqFAgBAo9HEYtHRowcXf7pibEJS2JCo8xdOaVd8kH23uVkwfNhI3eXrCW7CSCQSh2Mx7z+LgnuHYFg7XewkYvFHi75w6OKIYVj40OFlZSWana8tyspKGhsbEsZM9O7q6+nZddkXq9LS1mh+LACAl6d3n+B+JBKpe/eA0aMSL18+J5e3/rhWw917f9bUVM+aMc/GxtbDw2v+vE/4/KZ2X7s6f/4UFaOuTFvr4uLm5uax6L9LnxU+uX7j8quH+lLphAlTI8KHOzm5xETHlZS8eFb46j2aK1fO+/p0d3V1112+nuB5DvPx7q5nTmcXNybz1V5obs4GADQ3C3Tkd3Jy4XItV6Uv37V7e15eDplMDgoMNjd/1a/Iy8tHm9PRwVkul798Wa6jtOfPn9LpdHd3T81XOzt7W1u7doXl5+f4+vpZWHA1X+3tuzg4OD3MfaDN4Ovjp/ng59fDycnl/PlTmvnrr1y9EBkZo7tw/cHz/TAajaZnTjK5Y38oZmZm36//+cTJIwcz9/yyfZODg9O0KbMiI6P/l/pX9xM6gwEAEImEOkprbGxg/P2gTae3P3ypUNhc8ORRWHjw34pqqNd+fn3z40aN3bVne8rs+Q+y70ok4oiIEfptaPsY6IU+ZVtdFPTGxcVtTkrq9Gkp9+/fPnX66NervnB18/Du6vuGHqlE0q4ANpsjk/2tT5JE0uYBWaF8deC1suYFBAROn5byeqoFh9vqWpFRMVu2fn/33p83b10L7T+Iw+bovaHtQFSznkalicUi7deyspJ3Ka20tPjU6aMAADqdHho6aPmy1RiGPX36WJOak3NPm/NZ4RMMwxwdnXWU1sXeQSQSlZYWa75WvCyvra3RfDajmb3uTygU1tXVaj57enStqanq2aNXUGCw5p8l18rFxa3VKjhszpDBEVeunL948UxkRPS7bPsbECWsWzf/goL8oqJCzUlec3J+awQCfvqaFZu3fFdeUVZWVrJ7z68KhcLfr6cmtbau5sDB3UqlsrS0+PiJQ2FhUdpXxFqlf/9BNBptzbqVUqn0WeGTb1Z9oT0dOju7ss3ZJ0/9oVarFQrFqvRl7P/tHImJk1Qq1cZN66RSaVlZyU9bN7w/Y3zRi8K2aomOjtO0Ffv1G/gu2/4GRAmLGz0ufOjwWSmTwsKDT536Iznpfc0Z+O1K8/fvuXDBZ+cvnJo8JX7KtITc3Affrtvi5uahSY2Nic/PfxgRFTJ1eqKri/u8Dz/SXZq5uflXX66XSiSxowbPTkke9N5QHu9VH2cqlbp06TcFBflDI/pMnDRyyODILl0cNWFz2Jxftu1j0Bmz5yRPmZaQnXPvo0VLNcfkVgkKDMYwLDIiut02c4dovW/97TMNMinoOcQKx5oIYnR8eMKYiVMmz3iXQqZ/MK5nj16p8z/FLy7w5OnjOXOn7NyR6eTk0tF1r2ZWeQead+3VSvd6UxtFoDNQWPi0urpy67YfJk6Y+ha2dNOJhI0cNaStpE8+WT5wQJup/yQ3N/uz126MvcGu349oL6eIYOvPG+7cvRUZGf3+9Dm4F96JDomVVS/bSrLkWtHpHevrr6O0LvYOHQzN0MBxSMT3d+z8Vt4O9HgFMpAwyEDCIAMJgwwkDDKQMMhAwiADCYMMJAwyWr/TQaOTVACN02E0WByM3MY9qNb3MLYltbbENCf9hIKyJyIru9Y7yLQuzNbZjIR2MCMhl6vMLTHLDgljW1IdvehXM6sIjg3RCud+q+g11LKtVF3D7+Xf5D/LFvYcbG1pR6NgqHlCLC0SJb9WdutEbdg4GwePNnt9tTPA5Yt8UfaVpqoXUgoVpkOkSqUmkQAJnsO6ORcTNilcfZm9Iyx5Drp6EOk7M0SLBJohZAEA33//vbOz85gxY4wdiL6o1Wo6U68xX/V9gGnGgOqQSJaTMSVkMeuHCW6SaYOEQQYSBhlIGGQgYZCBhEEGEgYZSBhkIGGQgYRBBhIGGUgYZCBhkIGEQQYSBhlIGGQgYZCBhEEGEgYZSBhkIGGQgYRBhmkK43K5DEb7Y1bCiGkKa2pqkkhM8+0b0xRmwiBhkIGEQQYSBhlIGGQgYZCBhEEGEgYZSBhkIGGQgYRBBhIGGUgYZCBhkIGEQQYSBhn6joQDBXFxcWVlZa8vUalU3t7e+/btM15QOGNSe1hUVBTp73A4nGnTphk7LjwxKWETJkxwcfnb9E/u7u4jRuA2YWhnwKSEWVlZRUZGar+yWKykpCSjRoQ/JiUMADB27Fh391dTXLu7u0dFRRk7IpwxNWE8Hm/48OEYhrFYrAkTJhg7HPwxNWEAgISEBCcnJ1dX1+HDhxs7FvzRq1mvVKizjtdXFErIFNBUKzdIYO+EUqkkARKZAsGfo42jmUKudvFhhIyw1id/+8KaGxW/f1X83hg7tiWVY22mMqHrts4AiQSaaloE9fJ75+qmLXPDqO38kbUjjF8vP7yxIiG19fnAETgi4iuObCxJSffUna0dYSe3V/YYbG3Ba30MdQS+lBYI619KBsXb6MijaweUipXlzyTIlsHgOdILHwh159ElrKFK5ubfysy0CIJgsjGeg5mIr9SRR5cwlQIIGyBoE5oSdZUy3ScpCBq+iNdBwiADCYMMJAwykDDIQMIgAwmDDCQMMpAwyEDCIAMJgwwkDDIMJOyrrz+fN/8Dw9RlADIPZUREhRilarSHvSsvXjyfkBRrsOqQsHflydNHhqwOZ2HNwuYNG9dMSh4dHfvegoWzT5w8ok2iYtTs7Htjx4+IHNZvztwpjx7naZNu3Lgya/akYSNCx02I/uzzBdXVVQCAMYlRv+38WZOBz28KCw9OW/GpdpXEccP3ZvymI5LMQxkJY4ddv3E5PLLvDz+uBQA0NNR/+dWSCUmxcWMivvpmaVlZiTbzrT9vLFg4e0TMwEmT475Zvay+vg4A8LggPyw8+HFBvjZb8uS4TZvXv17Lrzu2rE5Pq66uCgsPPnBwt1qtPpi5Z+aspOHRA2anJP+8baNSqetp5FuAs7D09LRH+Q9TUxfv2H6wWzf/9d99k5//UJNUXVN19NjBzxavXPXNBplctmbtCs2Turv3/vxi+UdRUTH7M04uW7qqurryuw2rAADBwf0ePc7VrHv/wR07O/vcvGzN14qX5fX1dcHB/XREQqPRxGLR0aMHF3+6In70OKVSueC/s7Nz7i1I/Wz7tn2WXKu5/5la8bIcAPD0WcHiz+YHBfXZsf3g/837+Pnzp6vTl+u5vdOnpUwYP8XOzv7ShbtjEycdOpSxa/f2xISkjD3HR45MOHHySMa+ne/2i76JvvM460nOw/sTxk/pE9wPADBr5rzBgyMsOFxNUm1t9ZbNv7PN2QCAMfET1q77UiDgW1hwt/+6edB7QxMTkgAAFhbcuXMWLvpobsGTR72C+vywcY1arSaRSDk594YMjjzyx/6Kl+WODk65uQ+4XMuuXj46IiGRSFKpdMKEqb2C+gAAsrPvlZYWr1u7WfN1TkrqjawrmZl7/m/ex3m52XQ6PXnS+2Qy2c7O3tene9GLwrfefB+f7sOGxQIAYmPig4L6SMTityuqLXDewwICAvcf2LV5y3dZWVflcrmPdzd7+y6aJE9Pb40tAIDGolQqBQAUFT3z9fXTluDj3R0AUFCQ37tXiFgsfvHiOQAgNy87wD/Q19cvLzcbAJCbm927V1994vH1eVVybl42lUrACkuxAAAMiklEQVTV2NLoDOzZO+fhfQCAf0CgVCpdvCT1wMHd5RVlFhbcoMDgt9t8f/+e9+79mb5mxekzx/gCvqODk5eX99sV1RY472GffLz86NGDFy+d2X9glznLPD5+/JTJMzEMAwBo/tdAIpE0H4RCYUtLi5kZXZvEZDIBAGKxyMbG1tnZNS8/x9qa9+LF86CgPo8L8nLzsocNi32Y+2DC+Cn6xEOj0f5XUbNcLg8L/5sJLtcSAODd1XfVNxuuXr2w9ecfNm1e37tX32lTZ/v793yLzU9MSGIyWTeyrqxOT8MwbMiQyNkz/4/H09VtraPgLIzD5iRPen9S0vS8vJxr1y/9vusXc3P2uLHJbeWn0+kAAKn0r+FeRWIRAMDaigcA6N2r76PHuVyupYeHF5PJDAgI2rxlPZ/fVF5e2r/fex0KzNqax2Awvvryb00GCpmi+RDSNzSkb+j0aSn37v2ZeWjvZ0tSD2We+2chCqVCdy1kMjk2Jj42Jr64uOj+/ds7dm4ViYRf/73SdwRPYUKh8Oy5E9EjRtPp9ICAwICAwMLCJ0+fFeiqHsN8vLtpGyYAAM1nD8+uAIBevfpu3rzenMXu2bM3ACDAP7C0tPj8+VMuLm5WVnr1RNfi6ektkUhsbe0dHZw0S15WVnAtLDWntxZZS0jfUB7PZtiwWHt7h9SFs6qqK81oZgAAiUSs3bq6ulrdtZw5c9zbu5u7u6ebm4ebm0ezsPnEycMdirNd8DyHYRj2286ty1d8kpeX09BQf/bsiWeFBQH+gbrXio8bf/3G5czMvYJmwYPsu5s2f9srqI+mQREU2KequvLmzav+fj01R8uuXj6HDmf07t3huwy9e/Xt2zd07dqV1dVVfH7TkT8OpMyZfPr0UQBAXn7O8rSPjx0/1NTU+Ohx3qHDGTyejb1dF2dnV7Y5++SpP9RqtUKhWJW+jM3m/LNkJyeX+vq669cvl5WVXLh4+ovlH2VlXeUL+LduXb92/aImchzBcw+j0+krlq/54cc1mrtQ7u6eKbNTRwwfpXutqKiY2rqafQd+37hpnZ2dfXDvfjNnfKhJMjc39/HpXlCQr20s+Pn1OHxkv/Zrh/jmq++OHstc8eXiR49ynZ1dIyJGjBkzAQAwbmxyU1Pjxh/Xfrv+axqNNjRs2Ppvt2rOuEuXfvP9htVDI/rweDazZ81vaKj/Z6fBfiEDA/wDly5bNHXKrP8u/Hzjj2uXLF0IALCyso6NiR+b2Obp4O3Q1be+/Knk9pmGyCmO+FaJ0MGBb4vHLXAy57a5I6FbU5CBcyvRkCxekqq5LPsn0dFxc1JSDR6RIYBY2NIlXytVrd+po2JUg4djICAWprnE/reBzmGQgYRBBhIGGUgYZCBhkIGEQQYSBhlIGGToEqYGaiYH4itrGOHyqGrV244iYMGjVhab5tSfnROFXFVdKmVb6bqvpksYx4rKscIUchUBsSFaoalW5tGDpTtPO+ewHgMtrhyowjUqRJtcz6zqE2mlO0/7w+89viMouCMclGBHo1NwDQ/xF0K+4uKelxETbe1c6bpz6jXAZWGO8OG1pqZaeRd3hliAc99jIlCpVIBEIv+vM11nhm1FLXksdPBgBEdYtmurYxMNCJsUTbUyEgy/wt69e+3t7cPCwowdiB6QgJU9jcHS9+jVgVa7ORfT0degU6Eyq6NyWI5eDGMHgj/owhkykDDIQMIgAwmDDCQMMpAwyEDCIAMJgwwkDDKQMMhAwiADCYMMJAwykDDIQMIgAwmDDCQMMpAwyEDCIAMJgwwkDDKQMMgwTWFsNls7UqKJYZrCmpubZTKZsaMgBNMUZsIgYZCBhEEGEgYZSBhkIGGQgYRBBhIGGUgYZCBhkIGEQQYSBhlIGGQgYZCBhEEGEgYZHRgJp/MzYsSImpoalUpFJr/6Q1SpVM7OzkePHjV2aLhhUnvYkCFD1Go1hUIh/Q8qlTp27Fhjx4UnJiUsMTHR2dn59SWurq7jxo0zXkT4Y1LCPD09+/fvr/1KoVDi4uLMzMyMGhTOmJQwAMD48eOdnF7Ncunk5GRix0MTFObu7h4SEqKZkDMhIcH0OruZmjAAQFJSkrOzs6OjY2JiorFjwR9jNutlUlXxI1F9pUzIV4oECpUKKOX4BFNZWUmj0qx5HZs6uC0Y5hSVUs3iUMy5mK2zmbtfO+MoE4pxhOXf5Offaq5/2WLlzCaRyZgZBaNRyBRy5xzulATUcrlKIVMqWpSKFnljhcjJm+kfyvHqaW6EYAws7PGd5ht/1HEd2XQO3dwK1hFDBTUiKV8qE0kHjeG5+Bh0okDDCVMqwdGtVWKh2tbLikqHYyha3UgELbXPG3iOtBFTbA12aDCQsIaqlr3pZR4hjgyOqTXbBDXixtKGyUtcyGRDSDOEMFGzYu/qcs/+TiSDbJLhkQplL/Oqpy51pWCEbyDhwpob5Rlry7sOdCG0FqOjVKieXStNSfckuiLCr8N2ryrzCHEkuhajQ8HIzoH2GevKia6I2D3s7K5qOYnJsvq3TLjMfyno4qTuO6yd+VPeBQL3sIrnkupS+b/HFgDAwoHz4FKTVETgbCcECrt2uM7a3ZK48jsntp6W147UEVc+UcLKnojUFIzJbX/2F6OQnXt+0dIQoagR95ItnTg1FXIhX457yRqIElaYI6IxTepBlP6QqdTifDFRhRNUblGemG1jzJukRsTcmvnsgYigwgm5RVRf2cLhmdEYRN1/Ki59ePbStrLyR+Ysy24+A6PCZtDpLADAjVsHzl3ZPuf9zTszFlfXFHWx8xoUOrFPr1jNWsdP/3A356QZjRnUY5gtj8DrQrYNs6qWr1KqyBT89wdC9jARX9kiIWrmzLr6sp92zJPLWz6ctW1q0urK6mebt89RKhUAAApGlUiaj5xYOy7uszUrbvXwH7r/yJeNTVUAgKzbmVm3D46J+Wj+7F+tLR3OXfqFoPA0CBvlImJmxiNGmEBBxoiaf/F+zmmMQp02cbWdjZu9rcfY0UsqKp/kPb6iSVUq5ZFhM1ydA0gkUnBgjFqtrqh8CgC4fnN/D7/wHv5DmUxOn16xXh7BBIWngUrHCJrKkBBhUpESI+x+fHHpQ2en7iwWV/PVyrKLtZXTi5JsbQYXRz/NByaDAwCQSJvVanVdQ5mdrbs2j5ODL0HhaaCxMHGzgoiSCflZSWSSSkHUIVEiFZZVPFq0NOT1hYLm+r9q/8ejDmmLSKVSmpn9dQlPoxH7KE7RoqJghOwMhAhjsikqOVFX+2y2tbtr4LChs15fyGJZ6FiFbsYikylyuVS7pEVGVLNbg6JFyeQQclIgRBiLgykIE+Zg1/VezkkPtyBtf+yqmiIba12tPhKJZMntUlyaO3jAqyWPn9wgKDwNMomCxSHktyVkt+XaUElqog6Jg0InqlSqo6fWy2TSmtqS42c2rtuYVFldqHutnv4RuY8uZeeeBwBcvLazpDyPoPAAAEq5ksWlMswJ2cMIEcaywKg0krhJqkfeDsNkchZ9uIdGZXy3ZWr6hnFFxffHxi1ptxERMXh6SO/RR06uW7Q05PGTG6NGpAIACHpSwa8W2zkT9WCdqMcrd881FBUobb0IfNDQaSl/WDUglktQbziibk11DWKr5ETdAO3MqFVqMklNXN9Foq6WLHhUSxtKQ7nAyonTaoYmfs3ajRNbTWKYmUtahK0m2dt4fDjrZxzj/Pyr8LaSlEoFhdLK7+Pi5Ddr6oa21qp+1tCtL4E3UQl84iwVK3eklfgOcW01ValU8AU1rSbJZFIarfXnMmQyxrWwxTHIhsaXbSXJ5C00aisPHDCMxmHzWl1FLlUU33s580v3VlNxgdguAvcuNJa+UFk6comrolNR+7y+12CGZwCbuCqI7YTTO9wSU8sE1UQ9a+hU1L1odHSnEGrLEL2mRs7s0lzNb64j9s6C0aktamIylaEx+Lx+oQMD9fz9bWWJhSOX28UIbw8YgLoXjRZcdWSSjQHqMlzf+mM/V8lVVCsXkzqfKeWquuIGBxfsvTjC9y0NBn175cGlpqzjdXZdrXiuuu7VwkJ1YUNjuSB8ol3XQMMdOQz9upFSob6cWVdTLlcDCtuWyeZB1mtRrVILasXNtSKVTO7T2zxkuKFv5RjnhT6hQPE8W/T0vlAsVCkVKoyGUWgUChXrnKO8UDCSXCJ/9UKfXGnnyvDpxfLubU4hoMtGuxh5JBxZi0pQLxcLlCK+Qi5Tq1SdUxigmpFZHIzFwSztqMZ9TdSkhi76N2CCowiYNkgYZCBhkIGEQQYSBhlIGGT8P9fjBdHs8ZQ/AAAAAElFTkSuQmCC", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import os\n", "import sys\n", "import json\n", "import pandas as pd\n", "import nest_asyncio\n", "from typing import Any, Dict, List, Optional, TypedDict, Union\n", "from dotenv import load_dotenv\n", "from sqlalchemy import create_engine, text\n", "from pydantic import BaseModel, Field\n", "from langchain.output_parsers import PydanticOutputParser\n", "from langchain.prompts import ChatPromptTemplate\n", "from langchain_openai import ChatOpenAI\n", "from langgraph.graph import END, StateGraph, START\n", "from loguru import logger\n", "from langchain_core.runnables.graph import MermaidDrawMethod\n", "from IPython.display import Image, display\n", "\n", "\n", "\n", "# ----------------------- 0. Environment Setup -----------------------\n", "load_dotenv()\n", "nest_asyncio.apply()\n", "\n", "logger.remove()\n", "logger.add(sys.stdout, format=\"<level>{level}</level>: <cyan>{message}</cyan>\", colorize=True, level=\"DEBUG\")\n", "logger.level(\"DEBUG\", color=\"<blue>\")\n", "\n", "# ----------------------- 1. Schema -----------------------\n", "\n", "class LanguageItem(BaseModel):\n", "    language: str = Field(..., description=\"The language spoken by the candidate\")\n", "    proficiency: Optional[str] = Field(None, description=\"Proficiency level\")\n", "\n", "class ParsedQuery(BaseModel):\n", "    profession: Optional[str] = Field(None)\n", "    # skills: Optional[List[str]] = Field(None)\n", "    experience_years: Optional[Union[str, int]] = Field(None)\n", "    education: Optional[str] = Field(None)\n", "    languages: Optional[List[LanguageItem]] = Field(None)\n", "\n", "class GraphState(TypedDict):\n", "    query: str\n", "    parsed: Optional[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>]\n", "    retries: int\n", "    sql_query: Optional[str]\n", "    sql_results: Optional[List[Dict[str, Any]]]\n", "    error: Optional[str]\n", "\n", "# ----------------------- 2. Database Config -----------------------\n", "db_url = (\n", "    f\"mysql+pymysql://{os.getenv('MYSQL_USER')}:{os.getenv('MYSQL_PASSWORD')}\"\n", "    f\"@{os.getenv('MYSQL_HOST')}:{os.getenv('MYSQL_PORT', 3306)}/candidate\"\n", ")\n", "engine = create_engine(db_url)\n", "\n", "# ----------------------- 3. LL<PERSON> Setup -----------------------\n", "api_key = os.getenv(\"OPENAI_API_KEY\")\n", "llm = ChatOpenAI(model=\"gpt-4.1-mini\", temperature=0, api_key=api_key)\n", "\n", "parser = PydanticOutputParser(pydantic_object=ParsedQuery)\n", "format_instructions = parser.get_format_instructions()\n", "\n", "prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\n", "            \"system\",\n", "            \"\"\"You are a recruitment assistant. You will receive natural language queries from employers describing the kind of candidates they are looking for.\n", "\n", "Your job is to carefully understand and analyze these queries and extract the following structured information:\n", "- profession: the job title or role the employer is seeking\n", "- skills: a list of specific skills or qualifications mentioned\n", "- experience_years: number of years of experience required. If the query includes expressions like '5+ years', 'more than 3 years', or 'at least 2 years', capture the number and convert it to an integer.\n", "- education: any educational requirements, such as 'bachelor's degree', 'MBA', etc.\n", "- languages: any spoken or written languages mentioned, along with optional proficiency levels (e.g., B2, fluent)\n", "\n", "You must ignore irrelevant details and focus only on the fields listed above. If a particular field is not mentioned, leave it as null.\n", "Be strict and precise in your interpretation.\n", "\n", "Output your response in the exact format described in the instructions below.\n", "\"\"\",\n", "        ),\n", "        (\"user\", \"{query}\\n\\n{format_instructions}\"),\n", "    ]\n", ")\n", "\n", "\n", "# ----------------------- 4. G<PERSON>h <PERSON>des -----------------------\n", "def parse_query_node(state: GraphState) -> GraphState:\n", "    query = state[\"query\"]\n", "    retries = state.get(\"retries\", 0)\n", "\n", "    try:\n", "        formatted_prompt = prompt.format_messages(query=query, format_instructions=format_instructions)\n", "        response = llm.invoke(formatted_prompt)\n", "        parsed = parser.parse(response.content)\n", "\n", "        print(f\"\\n🔍 Attempt {retries+1} | Parsed:\\n{parsed}\\n\")\n", "\n", "        if not any([parsed.profession, parsed.experience_years, parsed.education, parsed.languages]):\n", "            return {**state, \"parsed\": None, \"retries\": retries + 1}\n", "\n", "        return {**state, \"parsed\": parsed, \"retries\": retries}\n", "\n", "    except Exception as e:\n", "        print(f\"❌ Error parsing: {e}\")\n", "        return {**state, \"parsed\": None, \"error\": str(e), \"retries\": retries + 1}\n", "\n", "def build_sp_params(parsed: ParsedQuery) -> Dict[str, Any]:\n", "    return {\n", "        \"keyword\": parsed.profession or '',\n", "        \"years\": int(parsed.experience_years or 0),\n", "        \"language\": parsed.languages[0].language if parsed.languages else '',\n", "        \"proficiency\": parsed.languages[0].proficiency if parsed.languages and parsed.languages[0].proficiency else '',\n", "        \"lang_code\": 'en',\n", "    }\n", "\n", "def query_sp_node(state: GraphState) -> GraphState:\n", "    parsed = state.get(\"parsed\")\n", "    if not parsed:\n", "        return state\n", "\n", "    params = build_sp_params(parsed)\n", "\n", "    try:\n", "        with engine.connect() as conn:\n", "            result = conn.execute(\n", "                text(\"CALL sp_filter_candidate_for_sureai(:keyword, :years, :language, :proficiency, :lang_code)\"),\n", "                params,\n", "            )\n", "            df = pd.DataFrame(result.fetchall(), columns=result.keys())\n", "            return {**state, \"sql_query\": str(result), \"sql_results\": df.to_dict(orient=\"records\")}\n", "    except Exception as e:\n", "        print(f\"❌ SQL Error: {e}\")\n", "        return {**state, \"error\": str(e)}\n", "\n", "def result_node(state: GraphState) -> GraphState:\n", "    results = state.get(\"sql_results\", [])\n", "    if not results:\n", "        print(\"⚠️ No results returned from SP.\")\n", "    else:\n", "        print(f\"✅ {len(results)} candidates found:\")\n", "        df = pd.DataFrame(results)\n", "        print(df.head())\n", "    return state\n", "\n", "# ----------------------- 5. G<PERSON>h Setup -----------------------\n", "workflow = StateGraph(GraphState)\n", "workflow.add_node(\"parse_query\", parse_query_node)\n", "workflow.add_node(\"run_sp_query\", query_sp_node)\n", "workflow.add_node(\"show_results\", result_node)\n", "\n", "workflow.set_entry_point(\"parse_query\")\n", "workflow.add_edge(\"parse_query\", \"run_sp_query\")\n", "workflow.add_edge(\"run_sp_query\", \"show_results\")\n", "workflow.add_edge(\"show_results\", END)\n", "\n", "app = workflow.compile()\n", "\n", "display(Image(app.get_graph().draw_mermaid_png(draw_method=MermaidDrawMethod.API)))"]}, {"cell_type": "code", "execution_count": null, "id": "1c64daf0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🚀 Starting search for: 'Find me a nurse with 3 years of experience'\n", "\n", "\n", "🔍 Attempt 1 | Parsed:\n", "profession='nurse' experience_years=3 education=None languages=None\n", "\n", "✅ 11 candidates found:\n", "                                   uuid                 name  \\\n", "0  b9cd7e4b-d650-11ef-9299-0a2404d5f241      <PERSON><PERSON>   \n", "1  b9cd7f78-d650-11ef-9299-0a2404d5f241       Rakibul Shaikh   \n", "2  b9cd7fdc-d650-11ef-9299-0a2404d5f241         <PERSON><PERSON><PERSON>   \n", "3  b9cd8049-d650-11ef-9299-0a2404d5f241  <PERSON>   \n", "4  b9cd810e-d650-11ef-9299-0a2404d5f241         <PERSON><PERSON>   \n", "\n", "                                       profile_photo country_name  \\\n", "0  candidates/b9cd7e4b-d650-11ef-9299-0a2404d5f24...        India   \n", "1  candidates/b9cd7f78-d650-11ef-9299-0a2404d5f24...        India   \n", "2  candidates/b9cd7fdc-d650-11ef-9299-0a2404d5f24...        India   \n", "3  candidates/b9cd8049-d650-11ef-9299-0a2404d5f24...        India   \n", "4  candidates/b9cd810e-d650-11ef-9299-0a2404d5f24...        India   \n", "\n", "       state_name total_experience_years  \n", "0  Andhra Pradesh                      4  \n", "1   Uttar Pradesh                      9  \n", "2       Karnataka                      3  \n", "3           Delhi                      3  \n", "4          Kerala                     11  \n", "\n", "📦 Final State:\n", "{\n", "  \"query\": \"Find me a nurse with 3 years of experience\",\n", "  \"parsed\": \"profession='nurse' experience_years=3 education=None languages=None\",\n", "  \"retries\": 0,\n", "  \"sql_query\": \"<sqlalchemy.engine.cursor.LegacyCursorResult object at 0x730eb225e060>\",\n", "  \"sql_results\": [\n", "    {\n", "      \"uuid\": \"b9cd7e4b-d650-11ef-9299-0a2404d5f241\",\n", "      \"name\": \"<PERSON><PERSON>\",\n", "      \"profile_photo\": \"candidates/b9cd7e4b-d650-11ef-9299-0a2404d5f241/profile_photo/78784a15-feb2-4d8e-9e84-7a592776b392.jpg\",\n", "      \"country_name\": \"India\",\n", "      \"state_name\": \"Andhra Pradesh\",\n", "      \"total_experience_years\": \"4\"\n", "    },\n", "    {\n", "      \"uuid\": \"b9cd7f78-d650-11ef-9299-0a2404d5f241\",\n", "      \"name\": \"<PERSON><PERSON><PERSON>\",\n", "      \"profile_photo\": \"candidates/b9cd7f78-d650-11ef-9299-0a2404d5f241/profile_photo/8932aa31-dee2-4cad-aa89-048d22009ec0.jpg\",\n", "      \"country_name\": \"India\",\n", "      \"state_name\": \"Uttar Pradesh\",\n", "      \"total_experience_years\": \"9\"\n", "    },\n", "    {\n", "      \"uuid\": \"b9cd7fdc-d650-11ef-9299-0a2404d5f241\",\n", "      \"name\": \"<PERSON><PERSON><PERSON>\",\n", "      \"profile_photo\": \"candidates/b9cd7fdc-d650-11ef-9299-0a2404d5f241/profile_photo/edc74655-bc3a-4135-a341-2f6eebaf75eb.jpg\",\n", "      \"country_name\": \"India\",\n", "      \"state_name\": \"Karnataka\",\n", "      \"total_experience_years\": \"3\"\n", "    },\n", "    {\n", "      \"uuid\": \"b9cd8049-d650-11ef-9299-0a2404d5f241\",\n", "      \"name\": \"<PERSON>ry\",\n", "      \"profile_photo\": \"candidates/b9cd8049-d650-11ef-9299-0a2404d5f241/profile_photo/dff92b2f-d797-47eb-a1ba-bd3e25e90d96.png\",\n", "      \"country_name\": \"India\",\n", "      \"state_name\": \"Delhi\",\n", "      \"total_experience_years\": \"3\"\n", "    },\n", "    {\n", "      \"uuid\": \"b9cd810e-d650-11ef-9299-0a2404d5f241\",\n", "      \"name\": \"<PERSON><PERSON>\",\n", "      \"profile_photo\": \"candidates/b9cd810e-d650-11ef-9299-0a2404d5f241/profile_photo/ec6cd3f7-5f38-4ea7-a1fd-7372f651f257.png\",\n", "      \"country_name\": \"India\",\n", "      \"state_name\": \"Kerala\",\n", "      \"total_experience_years\": \"11\"\n", "    },\n", "    {\n", "      \"uuid\": \"b9cd8176-d650-11ef-9299-0a2404d5f241\",\n", "      \"name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n", "      \"profile_photo\": \"candidates/b9cd8176-d650-11ef-9299-0a2404d5f241/profile_photo/d4e4a09b-c753-41ad-b94e-c6b6298e4ce6.jpg\",\n", "      \"country_name\": \"India\",\n", "      \"state_name\": \"Gujarat\",\n", "      \"total_experience_years\": \"9\"\n", "    },\n", "    {\n", "      \"uuid\": \"b9cd81db-d650-11ef-9299-0a2404d5f241\",\n", "      \"name\": \"<PERSON><PERSON><PERSON> R\",\n", "      \"profile_photo\": \"candidates/b9cd81db-d650-11ef-9299-0a2404d5f241/profile_photo/0aa12d8a-a66c-4b1e-95ca-7579b3abe65c.png\",\n", "      \"country_name\": \"India\",\n", "      \"state_name\": \"Andhra Pradesh\",\n", "      \"total_experience_years\": \"3\"\n", "    },\n", "    {\n", "      \"uuid\": \"b9cd831d-d650-11ef-9299-0a2404d5f241\",\n", "      \"name\": \"<PERSON><PERSON>\",\n", "      \"profile_photo\": \"candidates/b9cd831d-d650-11ef-9299-0a2404d5f241/profile_photo/4de42547-7c9a-4e10-9fb4-c2f7f73e11d7.jpg\",\n", "      \"country_name\": \"India\",\n", "      \"state_name\": \"Chandigarh\",\n", "      \"total_experience_years\": \"3\"\n", "    },\n", "    {\n", "      \"uuid\": \"b9cd8383-d650-11ef-9299-0a2404d5f241\",\n", "      \"name\": \"<PERSON><PERSON><PERSON>\",\n", "      \"profile_photo\": \"candidates/b9cd8383-d650-11ef-9299-0a2404d5f241/profile_photo/68e70cfa-9020-44eb-954f-0c3ed2b136ca.jpg\",\n", "      \"country_name\": \"India\",\n", "      \"state_name\": \"Kerala\",\n", "      \"total_experience_years\": \"11\"\n", "    },\n", "    {\n", "      \"uuid\": \"b9cd8530-d650-11ef-9299-0a2404d5f241\",\n", "      \"name\": \"<PERSON><PERSON><PERSON> chatter<PERSON>\",\n", "      \"profile_photo\": \"candidates/b9cd8530-d650-11ef-9299-0a2404d5f241/profile_photo/f4d50f73-7e4c-47bc-bf61-8f964375212b.png\",\n", "      \"country_name\": \"India\",\n", "      \"state_name\": \"Bihar\",\n", "      \"total_experience_years\": \"3\"\n", "    },\n", "    {\n", "      \"uuid\": \"f455baca-233d-46d2-ad26-17dc4b917b37\",\n", "      \"name\": \"<PERSON><PERSON><PERSON>\",\n", "      \"profile_photo\": \"candidates/f455baca-233d-46d2-ad26-17dc4b917b37/profile_photo/5a6b0fd4-4283-45b6-ac1b-4213928100e7.png\",\n", "      \"country_name\": \"India\",\n", "      \"state_name\": \"Tamil Nadu\",\n", "      \"total_experience_years\": \"5\"\n", "    }\n", "  ],\n", "  \"error\": null\n", "}\n"]}], "source": ["# ----------------------- 6. Run Example -----------------------from IPython.display import Image, display\n", "\n", "query = \"Find me a nurse with 3 years of experience\"\n", "# query = \"who is donald trump?\"\n", "\n", "print(f\"\\n🚀 Starting search for: '{query}'\\n\")\n", "None\n", "initial_state = {\n", "    \"query\": query,\n", "    \"parsed\": None,\n", "    \"retries\": 0,\n", "    \"sql_query\": None,\n", "    \"sql_results\": None,\n", "    \"error\": None,\n", "}\n", "\n", "final_state = app.invoke(initial_state)\n", "\n", "print(\"\\n📦 Final State:\")\n", "print(json.dumps(final_state, indent=2, default=str))"]}, {"cell_type": "code", "execution_count": null, "id": "eee636e5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "testing", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}