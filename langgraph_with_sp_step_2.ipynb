{"cells": [{"cell_type": "markdown", "id": "f79c24db", "metadata": {}, "source": ["###\n", "profession + exp = direct\n", "\n", "profession + language = direct"]}, {"cell_type": "code", "execution_count": 1, "id": "8be6e2d0", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAATcAAAHICAIAAACkhDqpAAAAAXNSR0IArs4c6QAAIABJREFUeJzt3XdcE+f/APAnix32BpkORBwooiKKFK2bCgJVUeuqu3XUrW1VHLVa7VetbWlVilZBFBVpRUXrAsWFKIKDvXeABJKQ9fvj+kupDRAgl7skn/fLP5Lc3XOfIB/u+dxz9xxFIpEgAACJUYkOAADQAchSAMgOshQAsoMsBYDsIEsBIDvIUgDIjk50AARoqheyqgVNjcLmRpFQIBaLiA5IDlq6VG1dqr4h3cCYbmajRXQ4QKkomjNeWlcpyM1g52U2aelQKQjpGdL1DWm6BjSRUAV+AlQapb5a0Nwo1NajleVxnT30Xfsb2PfSJTouoAwakaVNjaLUKzUikcTEQsvZQ9+yhzbREXULp16Yl9lUXcqvLef7TDG37wm5qubUP0uf3mRl3K33mWru5sUkOhYFqyzipybWGJoyAmZYEh0LwJGaZ2lCZJlrf4N+IwyJDgRHZbnchF/KZm1wNDTVxLMMmkCdszRqZ4F/mKWjmx7RgeBO0CI5+21h2JoeOvo0omMBiqe2WXpyR/7Uhbbm9qpdgnbKqT2Fk+bbwBlg9aOe46VXIss+CLPSqBRFCM3Z7Hh2fxHRUQDFU8Nj6dObLG09moda16JtYVW2PLrGGj/XiuhAgCKp27G0mS16fqdeM1MUIWRipUVjoNeP2UQHAhRJ3bI0NbFm5FRzoqMgks8U85QrNURHARRJrbKUVSUQCSRuQ9VtXLRT9Ji0QX7Grx42Eh0IUBi1ytK8FxxDM4aSdzpu3LjS0tLObpWbmztlyhR8IkI2zrqvn0CWqg/1ytJXTc4e+srcY3l5OYvF6sKGWVlZOITzN1sXnbryFn6zGL9dAGVSn3O8XLbo6m/lwSvt8WhcIpGcPXs2MTGxsLDQ2dl5+PDhy5YtS09PX7p0KbaCn5/fd999l5ube/78+cePH5eVlbm4uEybNi0kJARbISAgYNGiRbdu3UpPT58zZ86pU6ewz9esWRMeHq7wgFMSas3ttPsMMVB4y0D51OeasvpagRi3g0dMTMyJEydWr149cuTI27dv//DDD/r6+vPnz//+++9Xr159+fJlOzs7hNB3331XVla2detWCoVSUFCwb98+GxubkSNHIoQYDMbFixe9vb0XLVo0ZMgQCoVy/fr1xMREnALW0aPWVfARgixVB+qTpU0NQn1DvL7Os2fP3N3dsUoyKCho6NChzc3N/11t7969TU1Ntra2CCEvL6+EhITU1FQsSykUipGR0bp163CK8D36RnRWjowIgSpSnyxtbhTpG+J1FevAgQOPHDmyc+dOT0/P0aNH29vL7ldLJJKYmJiUlJTCwkLsE+wYi3F3d8cpvP/SM6Q1NarC7e1ADuqTpQhJGFp4ZemsWbP09fXv3LmzY8cOOp0+bty4zz//3MLCovU6YrF41apVLS0tK1eu9PLyYjKZCxcubL2ClpbyrrCl06k0GkVpuwO4Up8s1TWglxdwcGqcSqUGBQUFBQXl5eU9evQoMjKSw+EcOnSo9TqvX79+9erVsWPHvL29sU/YbLalJTF3frLrBVo6anUCX5OpT5bi2sdLTEzs27evq6uri4uLi4sLm82+ePHie+vU19cjhKRpmZeXl5eX5+rqilNI7cO1/w+UTH3+3DKNGbp6eP1eJiUlrV+//u7duw0NDffv379169bAgQMRQk5OTgihGzduZGZmuri40On0U6dONTY2FhQU7N+/f/jw4eXl5TIbdHBwqKmpuX37trSCVSyRUGJiCbewqQn1yVJDMzqruqWuogWPxrdt2+bi4rJ27dqAgICIiAg/P7+tW7cihOzt7adOnfrTTz8dOXLE2tp6165dL1++/OCDD9asWbNixYqQkJDMzEzpkGlrvr6+gwYNWrdu3bVr1/AI+NXDhh591P/2dw2hPlc1IIQe/FGrpUMdEmBCdCAEqylrST5TMWOdA9GBAMVQn2MpQsjFw4BVicuxVLWU5XL7DNHQe/fUkvqcPUIIWTlqpyWJil43O7Qx11FNTY3M/idCyMDAgMORfYrYxcXlxIkTCo30H1FRUVFRUTIXUSht9nSWLl06Y8aMttq8e7F65cGeiosREEyterwddvZEIlFlZaXMRTweT0dHR+YiOp2O34AKm81ms2XftN3Y2GhoKPuQaGhoaGAg++q/1MRabV3o9qsVdctShND9hBpbZ12X/kq9OYYkWviSpKjywCW2RAcCFEmt6lKMb6B5ypWa+moB0YEQIGZ/4ZgQCzlWBKpEDbMUITRrvYMGzqZ3+acy348slH8fPMCbGvZ4MUKB5OT2gpnrexgYq9UZsrYk/Fw2YrKZhYZNbqoh1PNYihCiMyhztznG/a+k5B2X6FjwxWsS/xZR0N/XCFJUXantsVTqdlx1Q22Lz1RzCzt1+yUWtkhSE2tYVQL/MEt4SIwaU/8sRQgVv2lOSazt0UvX0kHHxUOfRlf5W7pKc7jl+bwnyXU+U8wHjDIiOhyAL43IUkzey6a36ey8l019hjAZWhQ9Q7oek6arTxeJVGMWLzZL2NwopFAoL1MbLO21ew4y6D8S8lMjaFCWShW/4bKqWprYwuZGkUSCBHxFZmlNTU1DQ4PCb1jTZdLoDIo+k25oyujRRxfuHdUompiluLp69WpqampERATRgQD1AX+SASA7yFIAyA6yFACygywFgOwgSwEgO8hSAMgOshQAsoMsBYDsIEsBIDvIUgDIDrIUALKDLAWA7CBLASA7yFIAyA6yFACygywFgOwgSwEgO8hSAMgOshQAsoMsBYDsIEsBIDvIUgDIDrIUALKDLFUwOp3OZDKJjgKoFchSBRMKhWw2m+gogFqBLAWA7CBLASA7yFIAyA6yFACygywFgOwgSwEgO8hSAMgOshQAsoMsBYDsIEsBIDvIUgDIDrIUALKDLAWA7CBLASA7yFIAyI4ikUiIjkEdBAUFiUQiiUTC5XL5fL6JiQn2Ojk5mejQgMqjEx2AmvD09Lx48SKNRsPecrlcsVjcu3dvouMC6gB6vIoxb948Jyen1p/o6OiEhYURFxFQH5CliuHg4DBs2LDWn9jb2wcHBxMXEVAfkKUKM3PmTDs7O+y1trZ2eHg40REBNQFZqjCOjo6+vr7Ya3t7+48++ojoiICagCxVpLCwMDs7O21t7VmzZhEdC1AfqnSOt6lRVFPGF/DFRAfSDjNfz5DXr1/3cwrIyeAQHUybaDSKsaWWiSWD6ECAXFRjvJTLEd2Kra4o4jq6GfCaRESHo/L0jGmlb5v1jeiefsbOHvpEhwM6oAJZ2swWXfyhdFSQtYm1FtGxqBWJCN34vdRrrKljX12iYwHtUYG6NHp3waSFPSBFFY5CQx/OtUtLqi3L4xEdC2gP2bP08Q3WkABzuhaF6EDU1ogpVs9usYiOArSH7FlaUcA1MFalU1wqx9iCUZDVRHQUoD1kz1KRADFNoa+LJwqydtRtrBEQHQdoE9mztJktFIvIfn5L1XEaBYgKNQV5kT1LAQCQpQCQHWQpAGQHWQoA2UGWAkB2kKUAkB1kKQBkB1kKANlBlgJAdpClAJAdZCkAZAdZCgDZQZYCQHaQpQCQnbpl6dt3r/0DvO7eu7Xw0xn+AV4hYRN+OHZQujT+YuyGjSunBo6ZHjp+Z8Tm0rIS7PML8THTQ8ffT7kdMM77yA8HEEIP01LWrF0ycbJv+Jxpe/d9XVtbg61ZV1e7a/fWGbOmTAseu3vvl8XFhfJEdfPWtdlzpvkHeC1fOa+8osw/wCv5ZhJCKCY2euJkX+lqlZUV/gFeKSl3sLevXr3YsHFl4Ef+cz4JPvbjoaamv+/V/nr7hp0Rm3+OPOwf4BX1W6R/gFdmZoa0kZyct/4BXm/fvVbEjxOQgrplKZ1GRwidPn18V8TBa1dTVyz/4nJC3B9/XkIIvXz5/MjR/f36Ddy588CmjTtYrLrde7ZhW2lpaTU3NyUknN+8aWfQR2Fv373evGWVp+fQqBPnP/9sQ27u233fbkcIiUSiNV8seZ7xdM3qLSd+jTUxNl2+4hNpqrelqKhg955tAQETLl+6tWD+sj17v0QI0ekdTEBRUlq8bsNyHp939MjJiB0H8vLerVm7WCgUIoQYDEZefk5efs7uiIPTPgq1srJOvnlVuuGdu8lGRsY9XeFBUupDPScrGTXqAxtrW4SQ/5hxyTev3ryZNHnSNHf3/iePn7O3d8AyRCgQbNm2pqGxwcjQiEKh8Hi8GTM+Gew5FCEUHx+jo6MzO3wBlUq1srJ26+Oel5+D5XlRUcF3B37EVlu2dHVK6p0LF858/tmGdoK5dj3R2Nhk7pxPaTSa15BhdbU1rQ99bUlOvsqgMyJ2HDAyMkYIrfviy5nhU++n3B7jN5ZCoVRUlP107JSOjg5CaOqU6bGx0Z+tXI898e2v2zfGfziFSlW3v7+aTD3/L3v17CN9bWfbo6AwDyFEo9HKyko2b1k1JdDPP8Bry7Y1CKF6Vp10Tbc+/bAXHv0H8Xi8zVtXx53/vaS02MjI2HOQF0LoZeZzBoOBpShCiEKhDBo4JOPFs/aDycl506ePu/Shif08BiKEOpxg9dWrDDe3fliKIoSsrW1sbe1fvEzH3jo6OGMpihCaPGkap4mTlpaCEMrLyyktLZ40ER5+oVbU81iqo6Pb6rVOUxMHIZSScmfbV1+Ez5q/ZPEqV9deT56mbdi4svVWWlp/T7DUu5fbN3sP3717M/KXI8d+PDRksPe8T5Z4eAzkcNgCgcA/wKv1VsbGJu0HU1/PsrPrIX2rqyPX5LccDvv1m6z39sWqq/07VG3t1gGM9PG7eSvJx2f0nbvJvXu5OTo6y7MLoCrUM0s5HLb0NY/Hw5I28c+L/fsPWrRwxX/X+a9h3j7DvH3mz1v69GnahfizW7aujr9ww8zMXFdXd/euQ63XpFFp7QfDZBryW/jSt83c5rbWFIn/mbbf1My8f/9B8+ctbb2CkaGxzA0nT5q2I2JTI7vxfsrtSROntR8PUDnqmaXPM576+o7BXufkvHFx7okQamxssLayka5z796tNjd//pTfwh/m7WNubjF+/BRra9vVaxdXVJa7uvbmcrmWltZ2tvbYmmXlpcZGHRxLra1t0x6liMVirFbMyHgqXcRgaPH5fKFQiJXKRYX50kWuLr2u3/hj4IDB0gqzoCDP3t5B5i6GDRtpaGgUGxtdWJg/NmCCfD8koDLUsy59/ORB2qNUhND9lNvpz5+MHTsRIdTTtffjJw/Tnz8RCoVx53/H1qyoLP/v5pmvMrbv2HAlMb6+npWVnRl/Mcbc3MLaymbIYG9vb58DByIqKysaGuovXY5bumxOUlJC+8H4+Y2tqak+9uMhoVD48OH9c3GnpYvc3ftLJJKka1ewYZgzMVHSRSEh4WKx+Oix73g8XnFx4c+Rhxcs+hg7ifVfFApl4oTAC/FnfUaMlpayQG2oZ5bOmjHv+PEf/AO8vt6+ITh4xuRJ0xBCCxYsH+bts+3LtR9OGFFZWbFp4w63Pu6bNn+ODV22FhY6e/KkoKM/HAiaPm7N2sV6evqHDkZih7u9u7/38xu7c9fmacFj4y/GjB07MTh4RvvBDPUavmTx5w8e3B03fvjuPdtad2L7uvVbtnR1ZORh/wCvnbs2L5y/XHpiyZBpePzXWF0d3SXLZs+dN/15xtP1677s3cutrb34+Pjx+fwPx03u9g8PkA7Zn+Z0Zl+Rb5C1iZW8E2fn5eUs/HTG/w79MmCAJ86hdVF9PSto+rivvtzrP2acApuNiY1OSDh/+tSlLozBXDhcELzC3tBUPcsfNQD/MSrv+fOnZeUlv0VHbv/6WxgmVUuQpQpw5mzU2bNRMhc5OrkcPXwC171v2LSSRqMtXLB8mLcPrjsCRFG3Hi8h2Bx2W+M6dBrdwsJS6RF1DvR4SQ7+YxSAacBkGjCJjgKoLShjACA7yFIAyA6yFACygywFgOwgSwEgO8hSAMgOshQAsoMsBYDsIEsBIDuyZ6mJFYPoENSfsYUWjU4hOgrQJrJnqZYOraaMR3QU6ozLEdWW8fUNO5gXBhCI7Fnq1E+/vpIvx4qgiyoLuG5ehkRHAdpD9ix17a9P16I8uVFLdCDqqaaEn3G3ttdwkRzrAsKQ/c41zL1LNS18ibmtjpmtDoXsf1hUAJVCYVXxmxqEb57Uz9rg8OTp4x07duzatcvTk6QTXGg41chShFBuBic3s0nAF9eVt7S/pkgoamhsMDUxRficEGlpaRGLxdJJqzurrq7O1NRU0UF1jom1FpIgu566nmP+nsqssrKytLR08ODB0dHRjo6Ofn5+xEYI/kWiRkQikUQiOXz48Lt37/Dby/Tp0wsKCrq27dWrV4cPHx4YGFhTU6PouBTj9evXa9euffPmjUQiqaioIDocIJFIJCpzLO1QTExMUVHRhg3tPbKl+5qamqqqqpyduzh5fFxc3L59+8RisY2Nzb59+zw8PBQdoGKIRCIajTZjxgxHR8d9+/YRHY6mU4cij8/nV1dXl5SU4J2iCCF9ff0upyjW3cWmz66srPziiy+Sk5MVGp3CYE+1iYmJCQsLQwhlZWX973//q62Fc3jEUO0sFYlE27dvr6mpMTExWbduHd6743A4gYGB3WmBzf5neqTa2tq9e/eePn263S0INmTIEIRQnz59TE1NsVBzcmTP3A3wo9pZ+uOPP3p5ednZ2XX4OFCFSExM/Oijbj3O7L3DUUNDwy+//PLzzz93OzR80Wi0OXPmrFq1CiFUVFTk6+ublZVFdFAaRCXr0vT09MTExC+//JLoQDpt2bJlaWlp0llzRSKRmZkZafu9beHz+RUVFY6Ojrt27RowYEA3+xegQyp2LBWJRAihyMjIxYsXK3nXjY2N+fn5cqzYnubmZoSQWCymUCi2trbp6ekql6IIIW1tbUdHR4TQzJkzMzIyGhoa+Hx+dnY20XGpLVU6lp45c8bJycnHh5i5odesWRMcHDxq1KjuNBIcHFxXV3f79m2E0PXr1z/88EPFBUgkgUCwYMECJyeniIgIiURCocC1+4qkMvPxJiUlVVZWzpo1i5C9s9lsBweHbqYoQig+Pl76+unTpxwOJzg4uNvREY/BYJw6daqwsBAhlJyc/ODBgyVLllhZWREdl5og+7GUx+MdPXp03bp1bDabyVSrmalramr++uuv0NBQogNRvISEBKFQGBwcnJaWNnDgwC5fpwUwZK9LFy1a5O3tjRAiNkWPHz/O4yn4Bjpzc3O1TFGEUGBgINZHYLFYY8eOxY6xoMtIeix9/PhxVVXV5MmkeBrnzZs3r1+/jsclOM+ePSssLAwKClJ4y6TCYrFMTEwWLFgQEBAQHh5OdDiqh4zH0rdv3544cWL06NFEB/I3ExOTTZs24dHy4MGDDxw4wOer+Q20JiYmCKFdu3Y1NjZiV/anpqYSHZQqIdex9Lfffvvkk09qa2vNzMyIjkVJysvLtbS0NOf7YtdCb9682dLSctu2bTweD6rWDpEoS3fu3GliYvLZZ58RHci/nDp1ytTUlCR9b3XS0NBgZGR04cKF+/fvb9iwwcbGhuiIyIv4LG1oaLhz505gYCA5D6G+vr7Jycm4/r1fu3bt3LlzBw0ahN8uyOzevXt0On3EiBFXrlzx8vKCdP0vgutSNps9ffp0d3d3hBAJU1QsFt++fRvvLllgYOAff/yB6y7IbNSoUSNGjMAGXRcvXlxbW0v4kYNsCDuWpqWlOTg46OnpGRkZERKAPGpqaoyMjBgMmG1Uefh8PoPBCAgIWLBgwZw5c4gOhxSIOZYmJCRER0dbWlqSOUXfvHmzatUq5aRoRUVFZWWlEnZEftra2lQqNSEhAfvdePHixdWrV4kOimDKztLr169j9yv+8MMP2K3GpJWenr506VLl7ItCoSxYsEA5+1IJTCYTu9XG0dExNTX18OHDWNeG6LiIobwer0QimTJlyooVKyZNmqScPaqWM2fODB06tFevXkQHQkZCoZBOp588efLevXvffPONpaUl0REplTKytLa2tq6uztXVtbq6WlWuwM7Nza2vr8dmKgDk8eLFC319fVdX119++WXMmDEa8kcN9x5vZmbmrFmzzM3NqVSqqqQoQmjbtm2Ghsqe8f3kyZNNTU1K3qlqGTBggKurK0LIzs7uq6++wq6RIDoo3OGYpe/evcM6uteuXcOuEVMVNTU1CxYsUP7faYlEEh0dreSdqqhJkyadPXsWmx556NChMTExREeEI7x6vHl5eUePHj148CAejasroVCYmppKnguYVUhqaipR0wMoAV7HUolE0q9fP5wax1tiYuLDhw+Vv186nQ4p2jU+Pj6HDh0qLS0lOhBc4JWlrq6uCxcuxKlxvGVnZxNyS2Rtbe3+/fuVv1/18OTJEw6HQ3QUuMBrRpXa2tr8/HwvLy+c2sfV5MmT9fX1lb9fLpcLt3R12Zo1a+zs7IiOAhd41aWPHj2Kioo6duwYHo2rKy6Xm56ersb1FegavHq8ZmZmqjvYSFRdqqurCynaZVCXdhrUpV0AdWl3QF3aaVCXdgHUpd0BdWmnQV3aBVCXApmgLpUB6lJVBHVpp0Fd2gVQl3YH1KWdBnVpF0Bd2h1Ql3Ya1KVdAHUpkAnqUhmgLlVFUJd2GtSlXQB1aXdAXdppUJd2AdSl3QF1aadBXdoFUJcCmfA6lqp6XWpubj58+HAl7xfq0i4YN24chUKhUqlNTU06OjrYa3Nz89OnTxMdmsJAXSoD1KUqREtLq66urqamhsvlslisuro6FovV/ae2kwpeWVpbW/vkyROcGsfb5MmTlX8ghbq0azw9PUUiUetPXFxcsGccqw28sjQ3N/fEiRM4NY43d3d3R0dH5e/XzMxs/fr1yt+vSps7d27rk0YUCmXMmDEWFhaEBqVgMF4qA4yXqpDevXt7enpK3zo4OKjZgRTqUtmgLlUtc+fOxaZ6plAoAQEBanYghbpUNqhLVUuvXr2wkXl7e/uwsDCiw1E8vEZicnNzo6KiVPSqBux5qspHqrq0uVEkaBETHYW8gqfOTn/0ZtyYCQxk1FAjIDoceRmZy/VEP7yuasjNzb19+7aKdnqJGi8liYdX67LSGpgmDC5bJMfqoItMrLVL3ja5DjDwmWJmYNze8ZKwpwyT2f79+x0cHD7++GMl77e2tvbEiRNEHk4l6HJkmY2znmNfAz1DvPpZQEoklNRXtdyKKZ/+ub2RWZs/cKhLZdDYuvTyz2WO7sy+w4whRZWDRqeY2WqHrnU6f7iYUy9sazUYL5VBM8dL36VzTKy0XQcwiQpAk33wse2DP+raWgrjpTJo5nhpRSFPW5fUT2dXY0bmjLyX7LaWwnipDJo5XtrCE5tY6xC1dw3H0KbaOOtxWLJP18H9pTJo5v2lnAahSKgyQy/qp7acjyiyT+VCXSqDZtalgLSgLpVBM+tSQFpQl8qgmXUpIC2oS2XQzLoUkBbUpTJAXQpIBepSGaAuBaQCdakMUJcCUoG6VAaoSwGpQF0qA9SlgFSgLpUB6lJAKlCXygB1KSAVqEtlgLoUkArUpTJAXQpIBepSGaAuBaQCdakMUJfKIy8vxz/A6+HD+yFhExYtnokQmjjZNyY2WrrCt/t3Llk6G3s9LXjs5YTz0ad+DRjnPSXQb8fOTbW1NR3u4mFaypq1SyZO9g2fM23vvq+xTd6+e+0f4HX33q2Fn87wD/AKCZvww7GD8gR889a12XOm+Qd4LV85r7yizD/AK/lmEkIoJjZ64mRf6WqVlRX+AV4pKXewt69evdiwcWXgR/5zPgk+9uOhpqYm7POvt2/YGbH558jD/gFeUb9F+gd4ZWZmSBvJyXnrH+D16tULuX+c7YF5j2TQ2HmPOoXBYCCEok//+nHYnC/Wbutw5djYaCqVeunizd9OXniZ+Tzqt5/b3+Ttu9ebt6zy9BwadeL8559tyM19u+/b7QghOo2OEDp9+viuiIPXrqauWP7F5YS4P/681H5rRUUFu/dsCwiYcPnSrQXzl+3Z+yVCiE7v4LxMSWnxug3LeXze0SMnI3YcyMt7t2btYqFQiH2jvPycvPyc3REHp30UamVlnXzzqnTDO3eTjYyM+/b1aL99OcF8vDLAfLzyoFAoCKGhXsNDQ8LlWd/Orsfs8AUIIWTAHOo14u3b7PbXz3z5XEdHZ3b4AiqVamVl7dbHPS8/R7p01KgPbKxtEUL+Y8Yl37x682bS5EnT2mnt2vVEY2OTuXM+pdFoXkOG1dXWtD70tSU5+SqDzojYccDIyBghtO6LL2eGT72fcnuM31gKhVJRUfbTsVM6OjoIoalTpsfGRn+2cj2NRkMI/XX7xvgPp1CpijkKQl0qw+3bt58/f678/apiXdq7V1951+z9z5pMpmFTE6f99T36D+LxeJu3ro47/3tJabGRkbHnoH/+6Pfq2Uf62s62R0FhXvut5eS86dPHHUshhFA/j4EIoQ6nuX31KsPNrR+Woggha2sbW1v7Fy/TsbeODs5YiiKEJk+axmnipKWlYLVAaWnxpIkftd+4/KAuleHx48dv3rxR/n5Vqy7FaGlry7kmduyVX+9ebt/sPWxuZhH5y5E5c4PWrV/e+uino6Pb6rVOhzlfX8/SbbVJ69ft4HDYj5889A/wkv4rKyth1dViS1t/d2Njk5E+fjdvJWHd3d693BwdnTvzddsD46Ukolp1aYdE4u5OjT/M22eYt8/8eUufPk27EH92y9bV8RduYIs4nH9m3OPxeDodZR2Tachv4UvfNnOb5Qnb1My8f/9B8+ctbb2CkaGxzA0nT5q2I2JTI7vxfsrtSRPb6353FtSlMhBVHKpWXfpfWlra3Fa//cXF3TpP/vz5U34Lf5i3j7m5xfjxU6ytbVevXVxRWf730oynvr5jsNc5OW9cnHu235q1tW3aoxSxWIzVihkZT6WLGAwtPp8vFAqxk0lFhfnSRa4uva7f+GNAqzc4AAAgAElEQVTggMHSCrOgIM/e3kHmLoYNG2loaBQbG11YmD82YEJ3vvt7oC4lEVWsS1tzd+9/5+5NDoeDEDp1+nhNTVV3Wst8lbF9x4YrifH19ays7Mz4izHm5hbWVjbY0sdPHqQ9SkUI3U+5nf78ydixE9tvzc9vbE1N9bEfDwmFwocP75+LO906bIlEknTtCjYMcyYmSrooJCRcLBYfPfYdj8crLi78OfLwgkUftz6J1RqFQpk4IfBC/FmfEaOlpaxCQF0qw/79+2NjY5W/X1WsS1tbuWKdqYnZ1I/GjBs/nM/nBXzQreNJWOjsyZOCjv5wIGj6uDVrF+vp6R86GCkdO5k1Y97x4z/4B3h9vX1DcPCM9k/wYueilyz+/MGDu+PGD9+9Z1vrTmxft37Llq6OjDzsH+C1c9fmhfOXS08sGTINj/8aq6uju2TZ7Lnzpj/PeLp+3Ze9e7m1tRcfHz8+n//huMnd+eL/hdfTnFS6LiXqaU4lJSWfffbZxYsXlbxfzOWfy3oPMbbvpUfI3uWXl5ez8NMZ/zv0y4ABnnKsLlt9PSto+rivvtzrP2acAmOLiY1OSDh/+tSlLozBnD9UELraXubD16AulQHqUtBZz58/LSsv+S06cvvX3ypqmFQKryyFurQLVL0u7ayXL59v2bq6raWnT13qVHV35mzU2bNRMhc5OrkcPYzvvR8bNq2k0WgLFywf5q34/0F4fqkMmvn8UkJ6vOUVZW0twi4tkh+bw249PNManUa3sLDsfHRKRUCPV6XrUqKo2XipPDqbiu1gGjCZBur5WEeoS2WAuhSQCoyXkoim1aVATjBeKgOMlwJSgftLSUQD61IgD6hLZYC6FJAK1KUkAnUpkAnqUhmgLgWkAnUpiUBdCmSCulQGqEsBqUBdSiLE1qVMEwaV1rlJT4ACmdtqtzXpDNSlMmhmXaqtS6kr5xG1dw3XwhWXF3L1jWgyl0JdSiLE1qU2zrr85u7OVAS6hlXV0nOgQVtL8bon5tGjR1FRUceOHcOjcXXF5XLT09MJ7PQm/VZhZKHjMVKRs4EAefy+J3fhTheGtnJ7vFCXdgHh46UTPrFubmzJuFNXV86XY3XQXU0NwrLc5lO7cuZ95dxWisL9pbJp5v2lUhl367MfscViCbtOQGwknSKdH1BVWDroNtS0OHvoj/rIvP3zdnB/KYmQZLx04GjjgaONJRIkbFGlv+ALFizYtGlT7969iQ5EfhKGtlx/VmC8VAYYL0UIUSionT4YCYlRC11LtWKWN1SoS0mE8LoUkBOMl8qgmeOlgLRgvJRESFKXArKBulQGqEsBqUBdSiJQlwKZoC6VAepSQCpQl5II1KVAJqhLZYC6FJAK1KUkAnUpkAnqUhmgLgWkAnUpiUBdCmSCulQGqEsBqUBdSiJQlwKZoC6VAepSQCpQl5II1KVAJqhLZYC6FJAK1KUkAnUpkAnqUhmgLgWkAnUpiUBdCmSCulQGqEsBqUBdSiJQlwKZoC6VAepSQCpt9nj5/G7Nbt7Y2FhRUdHNyVG1tbW7vK1YLBYIujjps42NjbGxcZd/AgwGo2vTN0NdCmRqM0vZbHZ32hUIBHp6et1spDtZ2tLSwuFwurbthAkTuvMTYDKZXYsc6lIgE149XiqVymAwcGpcXUFdCmTCK0vpdLqenh5OjeONw+FwuVzl7xfqUiATXlnanbJQY0FdCmTCK0uFQmFzczNOjePNwMBAV1dX+fuFuhTIBHUpiUBdCmSCulSGrKysOXPmKH+/UJcCmaAulSE3N5eQ/UJdCmSS9zrehISEs2fPfvvtt7t27SosLHR2dg4KCvrwww+xpQ8ePDh9+nRxcbGhoaGrq+uKFSuMjY25XO7Ro0cpFMoHH3zw3XffcblcNze3RYsWubm5YVtdv379zz//LCgocHJy8vPzmzZtGoWC48Mnw8LCZs2adf/+/czMzLi4OCaTKTOA6OjoM2fOYKOmixcvplKpsbGxn3322a5du6ZOnZqbm6utrb17925pszt37qyrq/v++++7HyHUpUAmeY+lDAaDw+EcO3Zs9erVV69eHTVq1KFDh6qqqhBCz549i4iIGDt27KlTp7Zs2VJVVXX06FGsLqXT6dnZ2Tdv3jx8+PClS5e0tbUPHDiANfjXX38dPHiwZ8+eJ0+enDdv3sWLF3/66Sc8vymi0+lXr151dXXds2ePrq5uWwHMnTs3NDTU0tIyKSkpODhYS0uLy+X+8ccf69evDwwMHD9+fHp6el1dHdYmj8d79OhRQECAQiKEuhTI1Iker0AgCA8P79u3L4VCGTt2rEQiwXqG0dHRI0eODAoKMjIycnd3X7x48aNHj/Ly8rC6lMvlrlmzxsbGhk6njxkzpqSkBDv3m5SU5OHhsXLlShMTk0GDBs2ZM+fKlSssFgu/r0qhUJhM5rJlywYPHkyn09sJoKWlRSKRSLfi8XihoaH+/v52dnZ+fn66urp37tzBlj548AAhNGbMGIVECHUpkKlzdWmfPn2wFwYGBtjoP0IoPz9f+jlCCLt29/Xr11hd2qNHD+lpJOlWYrE4Kyur9X1tgwYNEovFmZmZCvpeskmvK24/gP92vKUbamlpffDBB7du3cLepqSkjBgxgslkKiQ8Ho+HdU9AFzg5OXXt8mny69z9pf/99W1qauLz+a0vW8VGGjkcDnbMlPmDa2lpEQgEUVFRUVFRrT+vr6/vZPydIx0caj8ABoPx3jfV0tKSvp44ceKVK1fKyspMTU0fP368adMmRYVHpVJXrlypqNY0TUFBgVgsJjoKXHT3LnAsP3k8nvQTLDnNzMzaGS/V0dHR1dUdO3asr69v689tbGy6GY+cuhOAi4uLm5vbtWvXevbsqaurO3ToUEVFpbSvD1RLd7OUTqf36tUrOztb+klWVhZ2f2n746UuLi4cDmfgwIHYW4FAUFFRYWFh0c145Nd+AGKxmMfj6ejoyNx2/PjxcXFxZWVl/v7+dLpi5rtgsVjbtm374YcfFNIaUCcK6McHBgampqZeunSJzWZnZGRERkYOGjTIxcWl/fHS+fPnP3jw4Nq1a1g1uHfv3o0bN7a0tHQ/Hjm1E4CdnV19fX1qampJSYnMbf38/Orq6h4/fjx+/HhFxZOUlOTi4qKo1oA6UcBxYOzYsbW1tefPn//pp58sLS0HDx48f/58oVDY/m0lHh4eR48ejY2NPX78OI/H69u37/bt27tzQ2lntRPA0KFD+/Xr9+23386ePdvMzOy/2+rp6Xl6elZXVzs5OSkqnqlTpxJy8TAgP4p0yOE9NTU13WlXKBS2tLR08yJBc3PzLm/L4/G6fBc4RigUUigUGo3230UtLS2zZ89esGABdr/4e7p2F7hIJJK5LyCn8PDwr776qvVwg9rAaw5BOp2uqIKNKGKxmMvlGhkZtf6wsrKyrKzs8uXLPXr0UGB399y5cwUFBRs2bFBUg0CdwHW8bdLS0qLRaO/1Nf7666/NmzezWKyNGzcq8HrG58+fT5o0SVGtATWD1+EOq0vfOxCpHOwyjNZmzJgxY8YMhe9oz549Cm8TqA24v7Q9IpGom3MpyqOurq60tBTvvQDVBfeXtodGo7HZ7LZOsCnKtm3bIEtBO9rs8f63s9cpjY2N1dXVrq6u3WmkO7S0tLr5FTDV1dUtLS0yx2Pa0qlOBJfLNTMz8/b27lJ0QCO0maVtXXYjpxcvXkRFRR07dqw7jXQHlUrt5lfA9O3bVxHhtElXVzciIgLXXQBVB8+J6diqVavw6/Teu3evvLwcp8aBeoDnxHRMT08vOTkZj5b5fP7mzZvhInvQPnh+ace2bt3q4eGBR8vFxcUwBgM6hFeW5ubmnjhxAqfGlczAwACnw13Pnj1Hjx6NR8tAnUBdKpcdO3bcv39fsW2KRKK9e/cqtk2glvC69sjV1ZXAYRiFGz58+I0bN967ZbybkpOTu3k/ANAQeGVpbW1tfn5+64mFVNr48eMVeG09xsnJSYHzPAA1BnWpvOrq6hR7tWCfPn1MTU0V2CBQV1CXyis1NVWBZWRaWtqRI0cU1RpQbzBeKq9x48ZVVFQoqrX4+Hh3d3dFtQbUG9Sl8tLW1lbg7PtbtmxR9dv6gNJAXdoJFRUVb9686X47EolEURNtA00AdWknGBgYLFmypPvtbNu2DadLDoFagrq0EwwMDJYvX15YWNjNdl6+fKmoB0ABTQB1aeeEhYV1v5GEhARFxAI0BdSlncPn87v5vUpKShoaGhQXEVB/eB1L1bIuxc70Pnr0aMCAAV3uJoSFhd2+fVvRcWkuT09P6WSOM2fOxOavnDRpkjrdbATX8Xba5s2bGxsbu7ZtVlbWokWLWj/BDXTTiBEjHjx40HrCcTs7u0WLFhEalIJBXdppjo6OXd7W3d0dLmZQrHnz5r1+/br1382RI0eq2RN3oC7tisOHD2PPQe+s+Ph4kUiEQ0Say9vbu3///tK3PXr0CA0NJTQixYPx0q6wsrKKj4/v7Fapqal37tyBh8Eo3CeffCKd5HHYsGHqV2q1+TQn0A6RSFRQUNDZ34Z79+5ZWVn17t0bt7g01xdffHHnzh07O7vvv//e2dmZ6HAUDOY96goajdaFP9ijRo2CFMVJeHg4k8kcPny4+qUojsfSR48eETsfL97i4+MrKyuXLVsm5/p5eXkPHz6cNWsWznEpTE0p/9lf9RWFPC5bSHQschGJRFQqTXFP2MKRpYOORIJc+xsMHC3XHRcwXtpFvr6+ixYtkj9LY2Ji3NzccA5KYYrecO9frh40xsxjpKmOvmo/4ZKEJAjVlfFqyvgJP5cFLrHtcH2oS5Xk8ePHgwcPVolTR2+fsV89YI+d3fFvD+imt08ai16zg1bYtb8a1KVd19TUJP/lDUOHDlWJFG3hSTIfNEKKKkdvL0MrJ73MBx38FsF4adc1NjaGh4fLs2Z0dPT169fxj0gByvO5VKoq1HbqwtBMqzC7qf11YLy062xsbIYOHVpQUNDhmmfOnFGVn0ZDrcDaSeUfaalCzGy0Oyw6oS7FnUgk4nK5CnlMoxI8vlHHbUKe/jC5oZJw6oXXo0s++dKpnXWgLu0WHo/36NGj9tehUCj6+vrKigioIahLu0VHR+f7779/+/ZtO+tMnTq1qqpKiUEBdQN1aXetXLmyurq6raVv3rzp06ePlZWVcoMCagXqUvAvUJcqGdSlShITE8NisWQuevPmjUAgUHpEQK1AXaoA9fX1Fy5c+O/nWVlZu3fvZjAYRAQF1AfUpQoQHh4u82aXgoKC+fPnExERUCsw75ECMJlMmY/0njRpEhHhAHUDdaliJCYmvncNYH19/Y0bN4iLCKgPqEsVo1evXtHR0a0/OXPmTHFxMXERAfUB95cqRp8+fSIiIgQCgfRckaWl5bhx44iOC6gDqEsV5r25PEJCQoiLBagVqEsV5u3btytWrMBe37179969e0RHRLDQjyf+evwHoqNQmAvxMWM/HEbIrqEuVZjevXuXlpZizws/evSonV0HN+AD1XXx0rm9+75W2u6gLlWkS5cuIYS4XO7WrVvVbHp10NqbN1nK3B08v1SRBAIBi8XS1dUdOHAg0bEoT1FRwY6dm4Kmj5sWPHbrl2tfvnwuXUSnM+Ivxn44YcSUQL9NW1Y1NP7zsLnoU7+Gz5k2fqLPnE+Cvzu4WywWFxUV+Ad4ZWQ8w1ZIvpnkH+B18dI56V78A7yysjPbieTr7Rt2Rmz+OfKwf4DX3Xu3EEKvXr3YsHFl4Ef+cz4JPvbjoaamv2dFkEgk5y+c+XTxrAmTRi5ZOvuXX49izxyIiY2eONlX2mBlZYV/gFdKyp3We1m9dvG164nXr//hH+D19t3rtppSIKhLFYnBYISHh69Zs0ZzxmBaWlpWr11Mo9H2fXPku/0/0mn0rdvW8Hg8bOmdu8lNTZx93xxZv+6rzMznJ0/+iH1+MuqnS5fPLVuy+nzctYULlt++cyPu/O8ODk6Wllavsl5g62RmPreyss76/7cvM58b6Bu49WnvKTsMBiMvPycvP2d3xMEB/T1LSovXbVjO4/OOHjkZseNAXt67NWsXC4VChFB8fMzp30+ETJ8VcyZx6tTpf/x5KSY2up2WW/v+YGTfvh4ffjj5r5tPevdy605TcsKrx5ubmxsVFaWWT3Nq35QpUxISEnr06EF0IEpSUlLEYtVND57Zu5cbQujrr77JePEMywSEkJ6e/pzZf3epUlLvvHiZjhBic9hnY35btnSNr+8YhNAYv7F5ee9O/348OGiG56Ch2f9/tMx48WzC+Kl/Xr2MvX358rmX13Aqtb3jCoVCqago++nYKR0dHYTQpctxDDojYscBIyNjhNC6L76cGT71fsrtMX5jM14869PHffz4KQihKZODPD2Hcpubu/YTUGBTbcHxOl5fX185VlQ38+fP78IjZFSXnV0PY2OTb77dfvr3E5mZGVQq1XOQl3T6mP4eg6RrGhkat/D5CKHi4kKBQNC3r4d0Ue/efTkcTmlp8WDPoVgmNzTUFxTkBU4Nqa2tqayswI6lgwd7dxiPo4MzlqIIoVevMtzc+mEpihCytraxtbXH2vfwGPj0adq3+3cmXbvS0NhgZ2vfs2cXHzugwKbaAuOlCqarq0t0CEqlra39v0O//PHnpfMXzhw/cczW1n7e3MXjxv19ATOd/s8vmPRZwHV1NQghHW0d6SJdXT2EEJfbPGTIsMbGhqKigrz8nF49+5iamrm793/x4pm3t09ZWYn3UJ8O49HS1pa+5nDYr99k+Qf8q0PHqqtFCIVMn6Wnp5+SemfftzvodPqYMeOWfPq5ublFF34CCmyqLfD8UtBdDg5Oy5aunj9v6bNnj64mJez55itHJxesAyyTvr4BQojL40o/aW5uQgiZmpqbmZk7O7u+ynqRk/u2/wBPhNCA/p6vsl5QaTRbGzsrK+tOBWZqZt6//6D585a2/tDI0BghRKVSp0wOmjI5qKAg79mzR1HRkU1NnD27Dr3Xgkjc8XkgOZvqDhgvBd1SVFRwNSkBmwLKx2f09q/30en0t2+z29nE1bU3jUZ79SpD+kl2dibTgGlhYYkQ8vQcmpHx7OWL9IEDBmN95hcv09PTH3t5De9sbK4uvaqqKgYOGOw5yAv7Z2Js6uDghBC6di0xPz8XIeTk5BIcPGN68MycnDcIIQZDi8/nS+vqosL8DvfSVlMKBPeXgm5hsxu/3b/zx5++LyktLi4u/P3MSaFQ6NGvvYEoQ6bhuLGTTv9+IjX1biO78fr1Py5eig0JCcfODA0eNDQj42lO7luspvXwGFRYmP/0aZo8Rel7QkLCxWLx0WPf8Xi84uLCnyMPL1j0cV5+DkLo5q2kr7avT02929DY8PDh/Xv3b2Exu7v3l0gkSdeuYMMwZ2KiZLZsZ9cjOzvzWfpjFquuraYUCOpS0C39+g1Yu2ZL1G8/n4s7jRDyGjLs4Hc/OTl1cEXHiuVfUKnUiN1bhEKhra39rJnzZ874BFs0eLB3RWW5g4OTiYkpQsjAwMDJySUvL8fTc2hnYzNkGh7/NTYm5rcly2YXFRW4ufVbv+5LrCv+xdptR384sPXLtQghU1OzKZODQkNmI4T6uvVbtnR1ZOTh7w7udnfvv3jRZ6vXLv7v3GBTJwe/fZu9fsOKfd8caaspBcJrdjKoS1UUzE6mZETOTgZ1KQCKAtfxAlUyNXBMW4s2btzuO7LNpSoN6lKgSs6cudLWIl0dtR2phvFSoEqYBkyiQyAA1KUAkB2MlwJAdlCXAkB2cH8pAGQHdSkAZAd1KQBkB3UpAGQHdSkAZAd1KfgXOoPK0KYQHYUGodIoRuZaHayD076hLlVRBkb0unI+0VFokIbqFoQ6uC8N6lLwL2Y2WpJnRAehSTj1AlvXDq5AhroU/IuptZaxGf357TqiA9EIAr748fUa7w87uJsX6lLwPt9p5kgsfnytRsAXEx2LOqsq4l08Wjiv3fu/MXB/KZBhVJD5s79YiZHFEolEj4nXL4liicRiKpWqEie+DIwZeS/ZvQcbzt3mxNDqOGS8ZlQBakAiRmyWgNMgJDoQuezYsWP+/PkODg5EB9IxOoNqYadNkbsjC/eXgjZRqMjQjGFoxiA6ELlwhMUmNsjWRQ3vBYe6FACyg/FSAMgOxksBIDsYLwWA7KAuBYDsoC4FgOygLgWA7KAuBYDsoC4FgOygLgWA7KAuBYDsoC4FgOygLgWA7KAuBYDsoC4FgOygLgWA7KAuBYDsoC4FgOygLgWA7KAuBYDsoC4FgOygLgWA7PCtS58+fQq5CpQgLS2NwWDo6+sTHQgu8DqWYuLj40NDQxFCIpEI1x0BzSQUCs+cORMcHBwdHb1u3Tp7e3uiI8IF7nPbV1ZWWllZFRYWHjx4cNGiRf3798d1d0BDvHnz5vz581euXAkLCwsJCVGJKe27THlPoEhJScnNzZ07d256erqFhYW6/tkDeEtKSoqLi+PxeCEhIUFBQUSHowwEPCfm1atXW7duXbt27ejRo5W8a6C6qqur4+Lizp075+vrGxoaOnDgQKIjUh7CnuZUXV1tYWGxatUqe3v7NWvW0Omq8WAvoHxpaWnnzp3LysoKDQ0NCwszMDAgOiJlI/iZa0KhMD4+3t/f38LCIj4+ftq0aVQqvie0gKoQCoXnzp07f/68jY1NWFiYn58f0RERhkRPRtyzZ8+zZ8/Onz/PZrOZTCbR4QDCaNSZIXmQKEulMjMzd+7cuXHjRhhr1TRXr149f/68Rp0ZkgcZsxQhlJeXV1BQ8MEHH/z555/m5ube3t5ERwRwVFVVdf78+XPnzo0aNSokJESjzgzJg6TnbFxcXFxcXLAXhw8fFovFw4cPb2hoMDIyIjo0oEjYmaHs7OyQkJDExEQNPDMkD5IeS9/D5/O1tbWXLFliYGCwf/9+OMOk6rAzQ3Fxcba2thp+ZkgeqpGlUnfv3vXx8amvr4+NjZ0xY4aZmRnREYHOefPmTVxcXGJiYlhYWGhoaI8ePYiOSAWoWJZixGLxb7/9lpOTs3v37tLSUjs7O6IjAh2TnhkKDQ2dNm0a0eGoEpXM0tYeP368du3a/fv3Dx8+nOhYgAxwZqj7VD5LEUJcLjc/P9/d3f3HH3+0tbX96KOPiI4IoPfODGnmNUOKog5ZKlVcXBwVFRUSEtK3b9/s7Oy+ffsSHZEmEggEcXFxcGZIgdQqS1tbsWKFQCCIjIyUSCQUCoXocDQCdmbojz/+CA0NhTNDCqS2WYoQys/Pd3Z2fvXqVWxs7Pz5852dnYmOSG1dvXo1Li6Oz+fDmSE8qHOWSv3555+lpaWffvppRkaGo6OjsbEx0RGpmAsXLhw7duzmzZvvfV5VVYV1bkeNGhUaGjpgwACCAlRzJL32SLEmTZqEvRAKhSEhId98842Xl9d760yZMkUikXz99ddwNeJ7njx5EhkZWVdX1/rDhw8fxsXFZWdnh4aGwjVDeNOIY+l7KioqrK2t161b17Nnz8WLF2NXMmF5a21t/f333/fs2ZPoGMmipKRk5cqVJSUlCCEbG5v4+Hjs4GlnZxcaGgpnhpRDE7MU09DQEBcXN336dCaT+ddff23cuBFL1x49epw4ccLExIToAIknEolCQ0OLioqknzAYDDgzpHyam6VSYrF45MiRAoFA+omDg0N8fDyhQZHC/PnzX758KX0rEonS09MJjUhDwWXriEql8ni81p8UFBTMmTOHuIhIYf369ZmZma0/odFoMFUVIeBYiqZNm4bVXRKJRCwWSwdX3d3dT58+3eHm9dWC0hxuXWULp0EkFEia2UL8Q+4KQ1MtkUCsb0QztmBY9tC2c9Vtf/2AgACxWMzn81taWqQ/FrFYDIdT5dOIc7zt43A4dnZ2DAZDIpFoa2ubmpqamZkZGRm1f+mSsEXy7HZ99qNGoQAZWTMRotK1GXRduoEeSf/qSShUkURYVyeqqRa/TK1tbmxxctcf5Gds7agtc/0DBw40NTWx2WwOh8PlclksVl1dXUNDg9IDB3As/X8cDkfe4QQJSkmsy7jLsu5tpm+io63PwD04HIiEYnZVc2Ml29CU5hdsbmKpkt9CQ0CWdk5ZHv9WXLW2ga6Fi5pcGtFY1VydV9dnCNM30JToWIBskKWdkPmg8XFyvfMQO6R21wVX57F0dYRTFloTHQiQAbJUXrkvm9OuNdj2syQ6ELw0VDQxKLxJ89T2C6ouyFK5vHrYmJHSZOuu5r/BDeUcMa8paLkt0YGAf4Hx0o5VFfMe36hX+xRFCBnZGEhoOncv1hAdCPgXyNKOSNDNczWOQzRlaiVTB6OaCnH+q2aiAwH/gCztwMOrtQw9HY26jVzfwvDepWqiowD/gCxtj1AgefZXvbmTZl15r63PYOhpZz9uJDoQ8DfI0vY8vVVv3Yu8U/5euPLt/iMz8WjZzNHk1QMOHi2DLoAsbc/bp2w9E9kX0Kk3LV16Y52AVSWQY12AO8jSNrFZQh5XrGOgRXQgxNA308t7CYdTUoCr7dtU+q7ZwhHHx6g+fpb44PHF8socG6ueg/qPHTViBnbfyanYLQhRBg+cEBu/k89vduzRf/L4lY49PBBCfH7z7+e/ysl7YmPVc8TQYPxiQwgxLQyqS9m47gLICY6lbWLVCIS43YX2LONa7MUIe9s+W9ZenDhu2d3UmMt/HsIWUan0wuKXT59fXbU0as9Xd+gMrZj4ndiic5d219QWL5l39JOZ+yqq8l6/TcErPoToWtTyfBiPIQXI0jZx6kU0Og2nxh89vezi6Bk8dQPTwLSXi9f4gMUpaXFszt8zgPH5zR8HbTMztaPR6IMHjK+uKeTzmxsaqzMyk/195zj28DBkmk0Zv5JB18EpPIQQXYvG5Yjwax/ID7K0TQK+mKGDy/1cYrE4v+hF717DpJ/0cvGSSMT5Bc+xt5YWTtraethrHR0mQqiZ24IA5REAAAOOSURBVFjHKkUIWVn+M6twDzscJ++n0igGxgx+M1xASjyoS9skFEgQVYxLy8IWkUiQlPxTUvJPrT9nN/19LKVQZPz1bGpuQAhpa+lJP9HS6mC+hW5is1roGnrujFwgS9tkYExn1eNSmGpp6Whr6Q0ZNGlAvw9af25m2t51iPp6RgihFsE/UzTx+E14hIcRCcR0BpVG16SrrsgKsrRNBsa0miq8CjNbm95cHrunyxDsrVAoqGWVGhtZtbOJibEtQqig6AXW0RUKBe9yH+nr43VdlJAv0mXCrwcpQF3aJnNbHSTGK0snjVuWmX0n7WmCWCzOL3x++tzWn0+uEApb2tnE2MjSyWHgtVuRVdWFAgH/97gvEZ6XF/ObBbZOOJ6dAvKDLG2Ts7teVSFeA4bOjoPWLIvOL3i+fd+En6M+4/I488P3MxgdXOc0c/rXDvb9vv9x7tZd/nq6ht6DAxFutwdzajkObvjWvUBOcBd4ey4eK6MzDZnmmvjLmv1XwaJdLgwtqEuJB8fS9rgPY/LZPDlWVDecWp7rQENIUZKA0wPt6TOEmXa1jm9p0NZ0nhmZN+Mu75G5SE/XsJkr++avYUM+mjrhc0UFmV/4/PjpL2QuEotFFApV5kOWR/vM/NB/UVttVufVBn4KM5WRBfR4O5CX2fQwqcG2n+yzr/wWblMTS/YiPldbW3ZXWUtLz0BfkROF1rHKOruJjraBnp6hzEWNlU0M1DxxHmQpWUCWduzaqSoxXV/XWFNOeFZkV0xbYqOjD9UQWcD/RMfGz7EszaoS8jXiotbi5+Wjp5lBipIK/GfIZfYmx8Jnne5VqpySzMrB/oa2LprSa1AV0OOVF58r/nVbXs8R9ir6YJgOFb+o8Jlk4tpfT451gVJBlnaCWISi9xSa2JsYWesTHYsicRtbSl5WfPCxpWt/tfpeagOytNPuxNfkvGiycDY1tFT5w04LV1iTXycRCgMX2zBNYFiOpCBLu6K+WnDvcg2vGVHoDH1TPV0jFZvBTMATsWuauKxmsUA4YopZz4HyPRISEASytOtqy1vyXzXlZHBodFpTo5CuTaPrMCQikl6vQ2NQBVy+SCCi0Sm8JkHPAQYuHvo9+qh8d0ATQJYqAJct5jQKmhtFXI6ohUfSARuGNlVLh6ZvSNNj0g3NoHOrSiBLASA7GC8FgOwgSwEgO8hSAMgOshQAsoMsBYDsIEsBILv/A+xtRH3B2mMRAAAAAElFTkSuQmCC", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import os\n", "import sys\n", "import json\n", "import pandas as pd\n", "import nest_asyncio\n", "from typing import Any, Dict, List, Optional, TypedDict, Union\n", "from dotenv import load_dotenv\n", "from sqlalchemy import create_engine, text\n", "from pydantic import BaseModel, Field\n", "from langchain.output_parsers import PydanticOutputParser\n", "from langchain.prompts import ChatPromptTemplate\n", "from langchain_openai import ChatOpenAI\n", "from langgraph.graph import END, StateGraph, START\n", "from loguru import logger\n", "from IPython.display import Image, display\n", "from langchain_core.runnables.graph import MermaidDrawMethod\n", "\n", "\n", "# ----------------------- 0. Environment Setup -----------------------\n", "load_dotenv()\n", "nest_asyncio.apply()\n", "\n", "logger.remove()\n", "logger.add(sys.stdout, format=\"<level>{level}</level>: <cyan>{message}</cyan>\", colorize=True, level=\"DEBUG\")\n", "logger.level(\"DEBUG\", color=\"<blue>\")\n", "\n", "# ----------------------- 1. Schema -----------------------\n", "\n", "class LanguageItem(BaseModel):\n", "    language: str = Field(..., description=\"The language spoken by the candidate\")\n", "    proficiency: Optional[str] = Field(None, description=\"Proficiency level\")\n", "\n", "class ParsedQuery(BaseModel):\n", "    profession: Optional[str] = Field(None)\n", "    # skills: Optional[List[str]] = Field(None)\n", "    experience_years: Optional[Union[str, int]] = Field(None)\n", "    education: Optional[str] = Field(None)\n", "    languages: Optional[List[LanguageItem]] = Field(None)\n", "\n", "class GraphState(TypedDict):\n", "    query: str\n", "    parsed: Optional[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>]\n", "    retries: int\n", "    sql_query: Optional[str]\n", "    sql_results: Optional[List[Dict[str, Any]]]\n", "    error: Optional[str]\n", "\n", "# ----------------------- 2. Database Config -----------------------\n", "db_url = (\n", "    f\"mysql+pymysql://{os.getenv('MYSQL_USER')}:{os.getenv('MYSQL_PASSWORD')}\"\n", "    f\"@{os.getenv('MYSQL_HOST')}:{os.getenv('MYSQL_PORT', 3306)}/candidate\"\n", ")\n", "engine = create_engine(db_url)\n", "\n", "# ----------------------- 3. LL<PERSON> Setup -----------------------\n", "api_key = os.getenv(\"OPENAI_API_KEY\")\n", "llm = ChatOpenAI(model=\"gpt-4.1-mini\", temperature=0, api_key=api_key)\n", "\n", "parser = PydanticOutputParser(pydantic_object=ParsedQuery)\n", "format_instructions = parser.get_format_instructions()\n", "\n", "prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\n", "            \"system\",\n", "            \"\"\"You are a recruitment assistant. You will receive natural language queries from employers describing the kind of candidates they are looking for.\n", "\n", "Your job is to carefully understand and analyze these queries and extract the following structured information:\n", "- profession: the job title or role the employer is seeking\n", "- skills: a list of specific skills or qualifications mentioned\n", "- experience_years: number of years of experience required. If the query includes expressions like '5+ years', 'more than 3 years', or 'at least 2 years', capture the number and convert it to an integer.\n", "- education: any educational requirements, such as 'bachelor's degree', 'MBA', etc.\n", "- languages: any spoken or written languages mentioned, along with optional proficiency levels (e.g., B2, fluent)\n", "\n", "You must ignore irrelevant details and focus only on the fields listed above. If a particular field is not mentioned, leave it as null.\n", "Be strict and precise in your interpretation.\n", "\n", "Output your response in the exact format described in the instructions below.\n", "\"\"\",\n", "        ),\n", "        (\"user\", \"{query}\\n\\n{format_instructions}\"),\n", "    ]\n", ")\n", "\n", "def parse_json_field(json_str: str) -> Optional[List[Dict[str, Any]]]:\n", "    try:\n", "        # Unescape and parse the JSON string\n", "        return json.loads(json_str)\n", "    except Exception as e:\n", "        logger.error(f\"Error parsing JSON field: {e}\")\n", "        return None\n", "\n", "# ----------------------- 4. G<PERSON>h <PERSON>des -----------------------\n", "def parse_query_node(state: GraphState) -> GraphState:\n", "    query = state[\"query\"]\n", "    retries = state.get(\"retries\", 0)\n", "\n", "    try:\n", "        formatted_prompt = prompt.format_messages(query=query, format_instructions=format_instructions)\n", "        # logger.info(f\"Formatted prompt: {formatted_prompt}\\n\\n\")\n", "        response = llm.invoke(formatted_prompt)\n", "        # logger.info(f\"LLM Response: {response.content}\\n\\n\")\n", "        parsed = parser.parse(response.content)\n", "        logger.info(f\"Parsed: {parsed}\\n\")\n", "\n", "        print(f\"\\n🔍 Attempt {retries+1} | Parsed:\\n{parsed}\\n\")\n", "\n", "        if not any([parsed.profession, parsed.experience_years, parsed.education, parsed.languages]):\n", "            return {**state, \"parsed\": None, \"retries\": retries + 1}\n", "\n", "        return {**state, \"parsed\": parsed, \"retries\": retries}\n", "\n", "    except Exception as e:\n", "        print(f\"❌ Error parsing: {e}\")\n", "        return {**state, \"parsed\": None, \"error\": str(e), \"retries\": retries + 1}\n", "\n", "def build_sp_params(parsed: ParsedQuery) -> Dict[str, Any]:\n", "    return {\n", "        \"uuid\": \"df2842bf-f831-47e1-9a75-242ecebcd21e\",\n", "        \"keyword\": parsed.profession or '',\n", "        \"years\": int(parsed.experience_years or 0),\n", "        \"language\": parsed.languages[0].language if parsed.languages else '',\n", "        \"proficiency\": parsed.languages[0].proficiency if parsed.languages and parsed.languages[0].proficiency else '',\n", "        \"lang_code\": 'en',\n", "    }\n", "\n", "def query_sp_node(state: GraphState) -> GraphState:\n", "    parsed = state.get(\"parsed\")\n", "    # logger.info(f\"Query SP Node: {parsed}\\n\")\n", "    if not parsed:\n", "        return state\n", "\n", "    params = build_sp_params(parsed)\n", "    logger.info(f\"SP Params: {params}\\n\")\n", "\n", "    try:\n", "        with engine.connect() as conn:\n", "            result = conn.execute(\n", "                text(\"CALL sp_filter_candidate_for_sureai(:uuid, :keyword, :years, :language, :proficiency, :lang_code)\"),\n", "                params,\n", "            )\n", "            df = pd.DataFrame(result.fetchall(), columns=result.keys())\n", "            return {**state, \"sql_query\": str(result), \"sql_results\": df.to_dict(orient=\"records\")}\n", "    except Exception as e:\n", "        print(f\"❌ SQL Error: {e}\")\n", "        return {**state, \"error\": str(e)}\n", "\n", "# def result_node(state: GraphState) -> GraphState:\n", "#     results = state.get(\"sql_results\", [])\n", "#     logger.info(f\"Result Node: {results}\\n\")\n", "#     # filtered_results = [{k: v for k, v in r.items() if k != \"name\"} for r in results]\n", "#     # logger.info(f\"Result Node (without name): {filtered_results}\\n\")\n", "\n", "#     if not results:\n", "#         print(\"⚠️ No results returned from SP.\")\n", "#     else:\n", "#         print(f\"✅ {len(results)} candidates found:\")\n", "#         df = pd.DataFrame(results)\n", "#         print(df.head())\n", "#     return state\n", "\n", "def result_node(state: GraphState) -> GraphState:\n", "    results = state.get(\"sql_results\", [])\n", "    logger.info(f\"Result Node: {results}\\n\")\n", "\n", "    if not results:\n", "        print(\"⚠️ No results returned from SP.\")\n", "    else:\n", "        print(f\"✅ {len(results)} candidates found:\")\n", "\n", "        # Clean and convert JSON fields\n", "        for r in results:\n", "            for key in [\n", "                \"candidate_employment_details\",\n", "                \"candidate_education_details\",\n", "                \"candidate_certificate_details\"\n", "            ]:\n", "                if key in r and isinstance(r[key], str):\n", "                    r[key] = parse_json_field(r[key])\n", "\n", "        # Now display or save cleaned result\n", "        df = pd.DataFrame(results)\n", "        print(df.head())\n", "\n", "    return {**state, \"sql_results\": results}\n", "\n", "\n", "def check_parsing_complete(state: GraphState) -> str:\n", "    if state[\"parsed\"] is not None:\n", "        return \"run_sp_query\"\n", "    elif state[\"retries\"] >= 3:\n", "        print(\"⚠️ Please search for a job title, skills, or relevant experience so I can assist you better!\")\n", "        return \"none\"\n", "    else:\n", "        return \"retry\"\n", "\n", "workflow = StateGraph(GraphState)\n", "workflow.add_node(\"parse_query\", parse_query_node)\n", "workflow.add_node(\"run_sp_query\", query_sp_node)\n", "workflow.add_node(\"show_results\", result_node)\n", "\n", "workflow.set_entry_point(\"parse_query\")\n", "workflow.add_edge(START, \"parse_query\")\n", "workflow.add_conditional_edges(\n", "    \"parse_query\",\n", "    check_parsing_complete,\n", "    {\n", "        \"run_sp_query\": \"run_sp_query\",\n", "        \"retry\": \"parse_query\",\n", "        \"none\": END\n", "    },\n", ")\n", "workflow.add_edge(\"run_sp_query\", \"show_results\")\n", "workflow.add_edge(\"show_results\", END)\n", "\n", "app = workflow.compile()\n", "\n", "display(Image(app.get_graph().draw_mermaid_png(draw_method=MermaidDrawMethod.API)))"]}, {"cell_type": "code", "execution_count": null, "id": "7c47cb65", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🚀 Starting search for: 'Find me a nurse with 2 years of experience'\n", "\n", "\u001b[1mINFO\u001b[0m: \u001b[36mParsed: profession='nurse' experience_years=2 education=None languages=None\n", "\u001b[0m\n", "\n", "🔍 Attempt 1 | Parsed:\n", "profession='nurse' experience_years=2 education=None languages=None\n", "\n", "\u001b[1mINFO\u001b[0m: \u001b[36mSP Params: {'uuid': 'df2842bf-f831-47e1-9a75-242ecebcd21e', 'keyword': 'nurse', 'years': 2, 'language': '', 'proficiency': '', 'lang_code': 'en'}\n", "\u001b[0m\n", "\u001b[1mINFO\u001b[0m: \u001b[36mResult Node: [{'uuid': 'f455baca-233d-46d2-ad26-17dc4b917b37', 'name': '<PERSON><PERSON><PERSON> K <PERSON>', 'profile_photo': 'candidates/f455baca-233d-46d2-ad26-17dc4b917b37/profile_photo/5a6b0fd4-4283-45b6-ac1b-4213928100e7.png', 'country_name': 'India', 'city_name': 'Coimbatore', 'state_name': 'Tamil Nadu', 'total_experience_years': Decimal('5'), 'candidate_employment_details': '[{\"title\": \"Registered Nurse\", \"company_name\": \"Apollo Hospitals Coimbatore\", \"description\": \"Provided comprehensive nursing care to an average of 8-10 patients per shift in the Medical-Surgical and Critical Care Units.\\\\n\\\\nAdministered medications (oral, IV, IM, SC) accurately, monitored patient responses, and documented effects, leading to a 15% reduction in medication errors on assigned shifts.\\\\n\\\\nAssisted physicians with patient examinations, diagnostic procedures (e.g., lumbar punctures, central line insertions), and minor surgical interventions.\\\\n\\\\nManaged and maintained patient medical records using the hospital\\'s EHR system, ensuring accuracy and confidentiality.\\\\n\\\\nEducated patients and their families on disease processes, medication regimens, and self-care techniques, improving patient compliance with discharge instructions by 20%.\\\\n\\\\nCollaborated effectively with multidisciplinary healthcare teams to ensure holistic and coordinated patient care for improved patient outcomes.\", \"start_date\": \"2021-06-01\", \"end_date\": null, \"employment_type\": null},{\"title\": \"Staff Nurse (Internship)\", \"company_name\": \"Lilavati Hospital, Mumbai,\", \"description\": \"Gained practical experience across various departments, including general medicine, surgery, pediatrics, and obstetrics.\\\\nAssisted senior nurses with patient care activities, vital signs measurement, and documentation.\\\\nDeveloped foundational skills in patient assessment and care planning under direct supervision.\", \"start_date\": \"2020-01-20\", \"end_date\": \"2021-05-20\", \"employment_type\": null}]', 'candidate_education_details': '[{\"title\": \"Masters in Science in Nursing\", \"description\": \"Relevant coursework: Anatomy, Physiology, Pharmacology, Medical-Surgical Nursing, Pediatric Nursing, Community Health Nursing, Nutrition.\", \"institution\": \"Christian Medical College, Vellore, India\", \"start_date\": \"2018-04-05\", \"end_date\": \"2020-06-10\", \"education_course_level\": \"Master’s Degree\"},{\"title\": \"Certification in Elderly Care\", \"description\": \"\", \"institution\": \"Goethe-Institut Max Mueller Bhavan, Mumbai\", \"start_date\": \"2017-01-04\", \"end_date\": \"2018-03-09\", \"education_course_level\": \"Professional/Industry‐Recognized Certifications\"}]', 'candidate_certificate_details': '[{\"title\": \"Registered Nurse (RN) License\", \"issued_by\": \"Maharashtra Nursing Council (MNC), India \", \"date_of_issuance\": \"2021-08-10\", \"date_of_expiry\": null, \"content\": \"\"},{\"title\": \"Basic Life Support (BLS) Certified \", \"issued_by\": \"Indian Resuscitation Council (IRC) \", \"date_of_issuance\": \"2023-05-10\", \"date_of_expiry\": null, \"content\": \"\"}]'}]\n", "\u001b[0m\n", "✅ 1 candidates found:\n", "                                   uuid       name  \\\n", "0  f455baca-233d-46d2-ad26-17dc4b917b37  Vipin K R   \n", "\n", "                                       profile_photo country_name   city_name  \\\n", "0  candidates/f455baca-233d-46d2-ad26-17dc4b917b3...        India  Coimbatore   \n", "\n", "   state_name total_experience_years  \\\n", "0  Tamil Nadu                      5   \n", "\n", "                        candidate_employment_details  \\\n", "0  [{'title': 'Registered Nurse', 'company_name':...   \n", "\n", "                         candidate_education_details  \\\n", "0  [{'title': 'Masters in Science in Nursing', 'd...   \n", "\n", "                       candidate_certificate_details  \n", "0  [{'title': 'Registered Nurse (RN) License', 'i...  \n", "\n", "📦 Final State:\n", "✅ Data saved to matched_data/matched_data.json and matched_data/matched_data.csv\n"]}], "source": ["# ----------------------- 6. Run Example -----------------------\n", "# query = \"Find me a registered nurse (RN) or licensed practical nurse (LPN) with 3 years of experience.\"\n", "# query = \".........\"  # none will be returned\n", "# query = \"Drivers with tanker or bulk cargo experience ready to join a German logistics\"\n", "# query = \"Find me a nurse\"\n", "# query = \"Find me a nurse with 2 years of experience who has B1 proficiency in German\"\n", "\n", "query = \"Find me a nurse with 2 years of experience\"\n", "\n", "print(f\"\\n🚀 Starting search for: '{query}'\\n\")\n", "\n", "initial_state = {\n", "    \"query\": query,\n", "    \"parsed\": None,\n", "    \"retries\": 0,\n", "    \"sql_query\": None,\n", "    \"sql_results\": None,\n", "    \"error\": None,\n", "}\n", "\n", "final_state = app.invoke(initial_state)\n", "\n", "print(\"\\n📦 Final State:\")\n", "# print(json.dumps(final_state, indent=2, default=str))\n", "\n", "# ----------------------- 7. Save Results -----------------------\n", "\n", "os.makedirs(\"matched_data\", exist_ok=True)\n", "\n", "with open(\"matched_data/matched_data.json\", \"w\") as f:\n", "    json.dump(final_state, f, indent=2, default=str, ensure_ascii=False)\n", "\n", "sql_results = final_state.get(\"sql_results\")\n", "if sql_results:\n", "    df = pd.DataFrame(sql_results)\n", "    df.to_csv(\"matched_data/matched_data.csv\", index=False)\n", "    print(\"✅ Data saved to matched_data/matched_data.json and matched_data/matched_data.csv\")\n", "else:\n", "    print(\"⚠️ No candidates to save.\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "633549dc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "ae4a4df1", "metadata": {}, "source": ["## testing code"]}, {"cell_type": "code", "execution_count": null, "id": "c60ddb13", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import json\n", "import pandas as pd\n", "import nest_asyncio\n", "from typing import Any, Dict, List, Optional, TypedDict, Union\n", "from dotenv import load_dotenv\n", "from sqlalchemy import create_engine, text\n", "from pydantic import BaseModel, Field\n", "from langchain.output_parsers import PydanticOutputParser\n", "from langchain.prompts import ChatPromptTemplate\n", "from langchain_openai import ChatOpenAI\n", "from langgraph.graph import END, StateGraph, START\n", "from loguru import logger\n", "from IPython.display import Image, display\n", "from langchain_core.runnables.graph import MermaidDrawMethod\n", "\n", "\n", "# ----------------------- 0. Environment Setup -----------------------\n", "load_dotenv()\n", "nest_asyncio.apply()\n", "\n", "logger.remove()\n", "logger.add(sys.stdout, format=\"<level>{level}</level>: <cyan>{message}</cyan>\", colorize=True, level=\"DEBUG\")\n", "logger.level(\"DEBUG\", color=\"<blue>\")\n", "\n", "# ----------------------- 1. Schema -----------------------\n", "\n", "class LanguageItem(BaseModel):\n", "    language: str = Field(..., description=\"The language spoken by the candidate\")\n", "    proficiency: Optional[str] = Field(None, description=\"Proficiency level\")\n", "\n", "class ParsedQuery(BaseModel):\n", "    profession: Optional[str] = Field(None)\n", "    # skills: Optional[List[str]] = Field(None)\n", "    experience_years: Optional[Union[str, int]] = Field(None)\n", "    education: Optional[str] = Field(None)\n", "    languages: Optional[List[LanguageItem]] = Field(None)\n", "\n", "class GraphState(TypedDict):\n", "    query: str\n", "    parsed: Optional[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>]\n", "    retries: int\n", "    sql_query: Optional[str]\n", "    sql_results: Optional[List[Dict[str, Any]]]\n", "    error: Optional[str]\n", "\n", "# ----------------------- 2. Database Config -----------------------\n", "db_url = (\n", "    f\"mysql+pymysql://{os.getenv('MYSQL_USER')}:{os.getenv('MYSQL_PASSWORD')}\"\n", "    f\"@{os.getenv('MYSQL_HOST')}:{os.getenv('MYSQL_PORT', 3306)}/candidate\"\n", ")\n", "engine = create_engine(db_url)\n", "\n", "# ----------------------- 3. LL<PERSON> Setup -----------------------\n", "api_key = os.getenv(\"OPENAI_API_KEY\")\n", "llm = ChatOpenAI(model=\"gpt-4.1-mini\", temperature=0, api_key=api_key)\n", "\n", "parser = PydanticOutputParser(pydantic_object=ParsedQuery)\n", "format_instructions = parser.get_format_instructions()\n", "\n", "prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\n", "            \"system\",\n", "            \"\"\"You are a recruitment assistant. You will receive natural language queries from employers describing the kind of candidates they are looking for.\n", "\n", "Your job is to carefully understand and analyze these queries and extract the following structured information:\n", "- profession: the job title or role the employer is seeking\n", "- skills: a list of specific skills or qualifications mentioned\n", "- experience_years: number of years of experience required. If the query includes expressions like '5+ years', 'more than 3 years', or 'at least 2 years', capture the number and convert it to an integer.\n", "- education: any educational requirements, such as 'bachelor's degree', 'MBA', etc.\n", "- languages: any spoken or written languages mentioned, along with optional proficiency levels (e.g., B2, fluent)\n", "\n", "You must ignore irrelevant details and focus only on the fields listed above. If a particular field is not mentioned, leave it as null.\n", "Be strict and precise in your interpretation.\n", "\n", "Output your response in the exact format described in the instructions below.\n", "\"\"\",\n", "        ),\n", "        (\"user\", \"{query}\\n\\n{format_instructions}\"),\n", "    ]\n", ")\n", "\n", "\n", "matching_prompt = ChatPromptTemplate.from_template(\n", "    \"\"\"\n", "You are a candidate matching engine\n", "You will receive:\n", "    - A natural language search query entered by an employer\n", "    - A list of candidates with structured information (profession, specialization, experience, education, languages, etc.)\n", "\n", "Your task is to return only the candidates who match the query exactly or closely, based on clear logical conditions. Additionally, compute a weighted match score for each candidate based on adaptive weight distribution.\n", "\n", "    - Employer's Search Query: \"{user_query}\"\n", "    - Candidate List: {candidate_list}\n", "\n", "Your Instructions:\n", "    1. Interpret the search query and extract filters:\n", "        - Profession (e.g., Nurse)\n", "        - Specialization(s) or Skills (e.g., ICU)\n", "        - Minimum years of experience (e.g., > 5 years)\n", "        - Required education level (if specified)\n", "        - Required language(s) and proficiency level (e.g., German B2)\n", "    \n", "    2. For each candidate, check the following:\n", "        - Required profession (or closely related title)\n", "        - Required specialization/skills (e.g., ICU)\n", "        - Required years of experience\n", "        - Required education level (if specified)\n", "        - Required language and minimum proficiency level\n", "        - If language proficiency is slightly higher (e.g., C1 instead of B2), accept it\n", "\n", "For candidates missing one or more of the four parameters (skills, experience, education, language), redistribute the weights proportionally among the remaining parameters:\n", "    - Default weights:\n", "        - Skills: 40%\n", "        - Experience: 20%\n", "        - Education: 20%\n", "        - Language: 20%\n", "    - Example redistribution:\n", "        - If experience is missing: Skills 46.67%, Education 26.67%, Language 26.67%\n", "        - If skills is missing: Experience 33.33%, Education 33.33%, Language 33.33%\n", "\n", "Calculate Match Score for each candidate using:\n", "    - Match Score = (match_percentage_for_each_parameter * weight_of_that_parameter)\n", "    - Sum all weighted scores to get the final score (e.g., Skill match = 80% * 0.4 = 32)\n", "\n", "Return a list of top 25 candidates sorted by Match Score in descending order.\n", "Output only those candidates whose match score is at least 10 (i.e., 10%).\n", "Return the result as a JSON array of matching candidates with their computed match score. Do not explain or add extra text.\n", "\n", "Edge Cases to <PERSON><PERSON>:\n", "    - Ignore candidates with entirely missing data across all parameters\n", "    - Accept equivalent or higher language levels (e.g., B2+ means B2, C1, C2 are valid)\n", "    - Use strict match for specializations\n", "    - If query says \"more than 5 years\", only include candidates with > 5 years\n", "    - If query says \"at least 5 years\", include candidates with 5 or more\n", "    - Handle queries that contain multiple specializations or language combinations\n", "\n", "Output Format (JSON Only):\n", "[{{\n", "    \"name\": \"Candidate Name\", \n", "    \"profession\": \"Nurse\", \n", "    \"experience_years\": 6, \n", "    \"skills\": [\"ICU\"], \n", "    \"education\": \"BSc Nursing\", \n", "    \"languages\": [{{\n", "        \"language\": \"German\", \n", "        \"proficiency\": \"B2\"}}], \n", "    \"match_score\": 82.4\n", "}}]\n", "\"\"\"\n", ")\n", "\n", "\n", "# ----------------------- 4. G<PERSON>h <PERSON>des -----------------------\n", "def parse_query_node(state: GraphState) -> GraphState:\n", "    query = state[\"query\"]\n", "    retries = state.get(\"retries\", 0)\n", "\n", "    try:\n", "        formatted_prompt = prompt.format_messages(query=query, format_instructions=format_instructions)\n", "        response = llm.invoke(formatted_prompt)\n", "        parsed = parser.parse(response.content)\n", "\n", "        print(f\"\\n🔍 Attempt {retries+1} | Parsed:\\n{parsed}\\n\")\n", "\n", "        if not any([parsed.profession, parsed.experience_years, parsed.education, parsed.languages]):\n", "            return {**state, \"parsed\": None, \"retries\": retries + 1}\n", "\n", "        return {**state, \"parsed\": parsed, \"retries\": retries}\n", "\n", "    except Exception as e:\n", "        print(f\"❌ Error parsing: {e}\")\n", "        return {**state, \"parsed\": None, \"error\": str(e), \"retries\": retries + 1}\n", "\n", "def build_sp_params(parsed: ParsedQuery) -> Dict[str, Any]:\n", "    return {\n", "        \"keyword\": parsed.profession or '',\n", "        \"years\": int(parsed.experience_years or 0),\n", "        \"language\": parsed.languages[0].language if parsed.languages else '',\n", "        \"proficiency\": parsed.languages[0].proficiency if parsed.languages and parsed.languages[0].proficiency else '',\n", "        \"lang_code\": 'en',\n", "    }\n", "\n", "def query_sp_node(state: GraphState) -> GraphState:\n", "    parsed = state.get(\"parsed\")\n", "    if not parsed:\n", "        return state\n", "\n", "    params = build_sp_params(parsed)\n", "\n", "    try:\n", "        with engine.connect() as conn:\n", "            result = conn.execute(\n", "                text(\"CALL sp_filter_candidate_for_sureai(:keyword, :years, :language, :proficiency, :lang_code)\"),\n", "                params,\n", "            )\n", "            df = pd.DataFrame(result.fetchall(), columns=result.keys())\n", "            return {**state, \"sql_query\": str(result), \"sql_results\": df.to_dict(orient=\"records\")}\n", "    except Exception as e:\n", "        print(f\"❌ SQL Error: {e}\")\n", "        return {**state, \"error\": str(e)}\n", "\n", "def match_candidates_node(state: GraphState) -> GraphState:\n", "    query = state[\"query\"]\n", "    raw_candidates = state.get(\"sql_results\", [])\n", "\n", "    if not raw_candidates:\n", "        print(\"⚠️ No candidates to match.\")\n", "        return state\n", "\n", "    try:\n", "        print(\n", "            f\"🤖 Processing {len(raw_candidates)} candidates through matching engine...\"\n", "        )\n", "\n", "        formatted_prompt = matching_prompt.format(\n", "            user_query=query,\n", "            candidate_list=json.dumps(raw_candidates, ensure_ascii=False),\n", "        )\n", "        \n", "        print(f\"the formatted prompt is {formatted_prompt}\\n\\n\")\n", "\n", "        response = llm.invoke(formatted_prompt)\n", "\n", "        # Debug: Print raw response\n", "        # print(f\"🔍 Raw LLM Response (first 200 chars): {response.content[:200]}\")\n", "\n", "        # Clean the response content\n", "        response_content = response.content.strip()\n", "\n", "        # Handle cases where LLM might wrap JSON in markdown code blocks\n", "        if response_content.startswith(\"```json\"):\n", "            response_content = (\n", "                response_content.replace(\"```json\", \"\").replace(\"```\", \"\").strip()\n", "            )\n", "        elif response_content.startswith(\"```\"):\n", "            response_content = response_content.replace(\"```\", \"\").strip()\n", "\n", "        # Check if response is empty\n", "        if not response_content:\n", "            print(\"⚠️ LLM returned empty response. Using original candidates.\")\n", "            return {**state, \"sql_results\": raw_candidates}\n", "\n", "        # Try to parse JSON\n", "        try:\n", "            matched_results = json.loads(response_content)\n", "        except json.JSONDecodeError as json_error:\n", "            print(f\"❌ JSON Parse Error: {json_error}\")\n", "            print(f\"🔍 Problematic content: {response_content[:500]}\")\n", "            # Fallback: return original candidates\n", "            print(\"🔄 Falling back to original SQL results...\")\n", "            return {**state, \"sql_results\": raw_candidates}\n", "\n", "        # Validate that we got a list\n", "        if not isinstance(matched_results, list):\n", "            print(\n", "                f\"⚠️ Expected list, got {type(matched_results)}. Using original candidates.\"\n", "            )\n", "            return {**state, \"sql_results\": raw_candidates}\n", "\n", "        # print(f\"🏆 Matching complete! Found {len(matched_results)} qualified candidates\")\n", "\n", "        # if matched_results:\n", "        #     print(f\"📋 Top {min(3, len(matched_results))} candidates preview:\")\n", "        #     for i, candidate in enumerate(matched_results[:3], 1):\n", "        #         name = candidate.get('name', 'Unknown')\n", "        #         score = candidate.get('match_score', 0)\n", "        #         print(f\"  {i}. {name} (Score: {score})\")\n", "        # else:\n", "        #     print(\"⚠️ No candidates passed the matching criteria.\")\n", "\n", "        return {**state, \"sql_results\": matched_results}\n", "\n", "    except Exception as e:\n", "        print(f\"❌ Matching Error: {e}\")\n", "        print(f\"🔄 Falling back to original SQL results...\")\n", "        # Return original candidates as fallback\n", "        return {**state, \"sql_results\": raw_candidates}\n", "\n", "def result_node(state: GraphState) -> GraphState:\n", "    results = state.get(\"sql_results\", [])\n", "    if not results:\n", "        print(\"⚠️ No results returned from SP.\")\n", "    else:\n", "        print(f\"✅ {len(results)} candidates found:\")\n", "        df = pd.DataFrame(results)\n", "        print(df.head())\n", "    return state\n", "\n", "def check_parsing_complete(state: GraphState) -> str:\n", "    if state[\"parsed\"] is not None:\n", "        return \"run_sp_query\"\n", "    elif state[\"retries\"] >= 3:\n", "        print(\"⚠️ Please search for a job title, skills, or relevant experience so I can assist you better!\")\n", "        return \"none\"\n", "    else:\n", "        return \"retry\"\n", "\n", "# workflow = StateGraph(GraphState)\n", "# workflow.add_node(\"parse_query\", parse_query_node)\n", "# workflow.add_node(\"run_sp_query\", query_sp_node)\n", "# workflow.add_node(\"show_results\", result_node)\n", "\n", "# workflow.set_entry_point(\"parse_query\")\n", "# workflow.add_edge(START, \"parse_query\")\n", "# workflow.add_conditional_edges(\n", "#     \"parse_query\",\n", "#     check_parsing_complete,\n", "#     {\n", "#         \"run_sp_query\": \"run_sp_query\",\n", "#         \"retry\": \"parse_query\",\n", "#         \"none\": END\n", "#     },\n", "# )\n", "# workflow.add_edge(\"run_sp_query\", \"show_results\")\n", "# workflow.add_edge(\"show_results\", END)\n", "\n", "# app = workflow.compile()\n", "# ------------------ ✅ Updated LangGraph Workflow ------------------\n", "\n", "workflow = StateGraph(GraphState)\n", "\n", "# Nodes\n", "workflow.add_node(\"parse_query\", parse_query_node)\n", "workflow.add_node(\"run_sp_query\", query_sp_node)\n", "workflow.add_node(\"match_candidates\", match_candidates_node)\n", "workflow.add_node(\"show_results\", result_node)\n", "\n", "# Entry\n", "workflow.set_entry_point(\"parse_query\")\n", "workflow.add_edge(START, \"parse_query\")\n", "\n", "# Parse branching: success → SP, fail → retry or END\n", "workflow.add_conditional_edges(\n", "    \"parse_query\",\n", "    check_parsing_complete,\n", "    {\n", "        \"run_sp_query\": \"run_sp_query\",\n", "        \"retry\": \"parse_query\",\n", "        \"none\": END\n", "    },\n", ")\n", "\n", "# Run SP → Matching → Show Results → END\n", "workflow.add_edge(\"run_sp_query\", \"match_candidates\")\n", "workflow.add_edge(\"match_candidates\", \"show_results\")\n", "workflow.add_edge(\"show_results\", END)\n", "\n", "# Compile graph\n", "app = workflow.compile()\n", "display(Image(app.get_graph().draw_mermaid_png(draw_method=MermaidDrawMethod.API)))\n"]}, {"cell_type": "code", "execution_count": null, "id": "bfa7343e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "testing", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}