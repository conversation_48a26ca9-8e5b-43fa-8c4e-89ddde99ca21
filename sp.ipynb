{"cells": [{"cell_type": "code", "execution_count": null, "id": "65d9e4f2", "metadata": {}, "outputs": [], "source": ["import json\n", "import os\n", "import sqlite3\n", "import sys\n", "from typing import Any, Dict, List, Optional, TypedDict, Union\n", "\n", "import nest_asyncio\n", "from dotenv import load_dotenv\n", "from IPython.display import Image, display\n", "from langchain.output_parsers import PydanticOutputParser\n", "from langchain.prompts import ChatPromptTemplate\n", "from langchain_core.runnables.graph import MermaidDrawMethod\n", "from langchain_openai import ChatOpenAI\n", "from langgraph.graph import END, StateGraph, START\n", "from loguru import logger\n", "from pydantic import BaseModel, Field\n", "\n", "load_dotenv()\n", "\n", "nest_asyncio.apply()\n", "\n", "\n", "logger.remove()\n", "\n", "logger.add(\n", "    sys.stdout,\n", "    format=\"<level>{level}</level>: <cyan>{message}</cyan>\",\n", "    colorize=True,\n", "    level=\"DEBUG\",\n", ")\n", "\n", "# Change colors for each level if needed\n", "logger.level(\"DEBUG\", color=\"<blue>\")\n", "logger.level(\"INFO\", color=\"<green>\")\n", "logger.level(\"WARNING\", color=\"<yellow>\")\n", "logger.level(\"ERROR\", color=\"<red>\")\n", "logger.level(\"CRITICAL\", color=\"<RED><bold>\")\n", "\n", "\n", "# ----------------------- 1. Schema -----------------------\n", "\n", "\n", "class LanguageItem(BaseModel):\n", "    language: str = Field(..., description=\"The language spoken by the candidate\")\n", "    proficiency: Optional[str] = Field(\n", "        None, description=\"Proficiency level in the language (e.g., B2, C1)\"\n", "    )\n", "\n", "\n", "class ParsedQuery(BaseModel):\n", "    profession: Optional[str] = Field(\n", "        None, description=\"The profession or job title mentioned\"\n", "    )\n", "    skills: Optional[List[str]] = Field(\n", "        None, description=\"List of skills required for the job\"\n", "    )\n", "    experience_years: Optional[Union[str, int]] = Field(\n", "        None,\n", "        description=\"Years of experience required, e.g., '5+', 'more than 3', '5 plus', 'at least 2 years', 'less than 2 years\",\n", "    )\n", "    education: Optional[str] = Field(None, description=\"Education requirements\")\n", "    languages: Optional[List[LanguageItem]] = Field(\n", "        None, description=\"List of languages and their proficiency levels\"\n", "    )\n", "\n", "\n", "class GraphState(TypedDict):\n", "    query: str\n", "    parsed: Optional[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>]\n", "    retries: int\n", "    sql_query: Optional[str]\n", "    sql_results: Optional[List[Dict[str, Any]]]\n", "    error: Optional[str]\n", "\n", "\n", "# ----------------------- 2. Database Config -----------------------\n", "\n", "DB_PATH = \"databases/candidates_details_500.db\"\n", "\n", "\n", "def get_table_schema():\n", "    try:\n", "        conn = sqlite3.connect(DB_PATH)\n", "        cursor = conn.cursor()\n", "        cursor.execute(\"PRAGMA table_info(candidates)\")\n", "        schema = cursor.fetchall()\n", "        conn.close()\n", "        return schema\n", "    except Exception as e:\n", "        print(f\"Error getting schema: {e}\")\n", "        return None\n", "\n", "\n", "def execute_sql_query(sql_query: str) -> List[Dict[str, Any]]:\n", "    try:\n", "        conn = sqlite3.connect(DB_PATH)\n", "        conn.row_factory = sqlite3.Row\n", "        cursor = conn.cursor()\n", "        cursor.execute(sql_query)\n", "        results = [dict(row) for row in cursor.fetchall()]\n", "        conn.close()\n", "        return results\n", "    except Exception as e:\n", "        print(f\"SQL Error: {e}\")\n", "        return []\n", "\n", "\n", "# ----------------------- 3. LLM Config -----------------------\n", "\n", "api_key = os.getenv(\"OPENAI_API_KEY\")\n", "llm = ChatOpenAI(model=\"gpt-4.1-mini\", temperature=0, api_key=api_key)\n", "\n", "parser = PydanticOutputParser(pydantic_object=ParsedQuery)\n", "format_instructions = parser.get_format_instructions()\n", "\n", "prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\n", "            \"system\",\n", "            \"\"\"You are a recruitment assistant. You will receive natural language queries from employers describing the kind of candidates they are looking for.\n", "\n", "Your job is to carefully understand and analyze these queries and extract the following structured information:\n", "- profession: the job title or role the employer is seeking\n", "- skills: a list of specific skills or qualifications mentioned\n", "- experience_years: number of years of experience required. If the query includes expressions like '5+ years', 'more than 3 years', or 'at least 2 years', capture the number and convert it to an integer.\n", "- education: any educational requirements, such as 'bachelor's degree', 'MBA', etc.\n", "- languages: any spoken or written languages mentioned, along with optional proficiency levels (e.g., B2, fluent)\n", "\n", "You must ignore irrelevant details and focus only on the fields listed above. If a particular field is not mentioned, leave it as null.\n", "Be strict and precise in your interpretation.\n", "\n", "Output your response in the exact format described in the instructions below.\n", "\"\"\",\n", "        ),\n", "        (\"user\", \"{query}\\n\\n{format_instructions}\"),\n", "    ]\n", ")\n", "\n", "\n", "sql_generation_prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\n", "            \"system\",\n", "            \"\"\"You are an expert SQL assistant. Your job is to create a SQLite SQL query to find candidates from the 'candidates' table, \n", "based on the structured hiring requirements provided. Generate a complete SQL query starting with:\n", "\n", "SELECT * FROM candidates WHERE ... \n", "\n", "Schema of the table is below:\n", "{schema}\n", "\n", "All string comparisons (such as using LIKE) must be **case-insensitive**, either by using LOWER() on both sides \n", "or by adding COLLATE NOCASE.\n", "\n", "Return only the SQL query, without any explanation or commentary.\n", "\"\"\",\n", "        ),\n", "        (\"user\", \"Structured hiring criteria:\\n{parsed_data}\"),\n", "    ]\n", ")\n", "\n", "\n", "matching_prompt = ChatPromptTemplate.from_template(\n", "    \"\"\"\n", "You are a candidate matching engine\n", "You will receive:\n", "    - A natural language search query entered by an employer\n", "    - A list of candidates with structured information (profession, specialization, experience, education, languages, etc.)\n", "\n", "Your task is to return only the candidates who match the query exactly or closely, based on clear logical conditions. Additionally, compute a weighted match score for each candidate based on adaptive weight distribution.\n", "\n", "    - Employer's Search Query: \"{user_query}\"\n", "    - Candidate List: {candidate_list}\n", "\n", "Your Instructions:\n", "    1. Interpret the search query and extract filters:\n", "        - Profession (e.g., Nurse)\n", "        - Specialization(s) or Skills (e.g., ICU)\n", "        - Minimum years of experience (e.g., > 5 years)\n", "        - Required education level (if specified)\n", "        - Required language(s) and proficiency level (e.g., German B2)\n", "    \n", "    2. For each candidate, check the following:\n", "        - Required profession (or closely related title)\n", "        - Required specialization/skills (e.g., ICU)\n", "        - Required years of experience\n", "        - Required education level (if specified)\n", "        - Required language and minimum proficiency level\n", "        - If language proficiency is slightly higher (e.g., C1 instead of B2), accept it\n", "\n", "For candidates missing one or more of the four parameters (skills, experience, education, language), redistribute the weights proportionally among the remaining parameters:\n", "    - Default weights:\n", "        - Skills: 40%\n", "        - Experience: 20%\n", "        - Education: 20%\n", "        - Language: 20%\n", "    - Example redistribution:\n", "        - If experience is missing: Skills 46.67%, Education 26.67%, Language 26.67%\n", "        - If skills is missing: Experience 33.33%, Education 33.33%, Language 33.33%\n", "\n", "Calculate Match Score for each candidate using:\n", "    - Match Score = (match_percentage_for_each_parameter * weight_of_that_parameter)\n", "    - Sum all weighted scores to get the final score (e.g., Skill match = 80% * 0.4 = 32)\n", "\n", "Return a list of top 25 candidates sorted by Match Score in descending order.\n", "Output only those candidates whose match score is at least 10 (i.e., 10%).\n", "Return the result as a JSON array of matching candidates with their computed match score. Do not explain or add extra text.\n", "\n", "Edge Cases to <PERSON><PERSON>:\n", "    - Ignore candidates with entirely missing data across all parameters\n", "    - Accept equivalent or higher language levels (e.g., B2+ means B2, C1, C2 are valid)\n", "    - Use strict match for specializations\n", "    - If query says \"more than 5 years\", only include candidates with > 5 years\n", "    - If query says \"at least 5 years\", include candidates with 5 or more\n", "    - Handle queries that contain multiple specializations or language combinations\n", "\n", "Output Format (JSON Only):\n", "[{{\n", "    \"name\": \"Candidate Name\", \n", "    \"profession\": \"Nurse\", \n", "    \"experience_years\": 6, \n", "    \"skills\": [\"ICU\"], \n", "    \"education\": \"BSc Nursing\", \n", "    \"languages\": [{{\n", "        \"language\": \"German\", \n", "        \"proficiency\": \"B2\"}}], \n", "    \"match_score\": 82.4\n", "}}]\n", "\"\"\"\n", ")\n", "\n", "\n", "# ----------------------- 4. LangG<PERSON><PERSON>des -----------------------\n", "\n", "\n", "def parse_query_node(state: GraphState) -> GraphState:\n", "    query = state[\"query\"]\n", "    retries = state.get(\"retries\", 0)\n", "\n", "    try:\n", "        formatted_prompt = prompt.format_messages(\n", "            query=query, format_instructions=format_instructions\n", "        )\n", "        # Log formatted system and user messages separately\n", "        # for msg in formatted_prompt:\n", "        #     if msg.type == \"system\":\n", "        #         logger.info(f\"SystemMessage:\\n{msg.content}\\n\")\n", "        #     elif msg.type == \"human\":\n", "        #         logger.info(f\"HumanMessage:\\n{msg.content}\\n\")\n", "        response = llm.invoke(formatted_prompt)\n", "        parsed = parser.parse(response.content)\n", "\n", "        print(f\"🔍 Attempt {retries+1} | Parsed:\\n{parsed}\\n\")\n", "\n", "        if not any(\n", "            [\n", "                parsed.profession,\n", "                parsed.skills,\n", "                parsed.experience_years,\n", "                parsed.education,\n", "                parsed.languages,\n", "            ]\n", "        ):\n", "            return {\n", "                \"query\": query,\n", "                \"parsed\": None,\n", "                \"retries\": retries + 1,\n", "                \"sql_query\": None,\n", "                \"sql_results\": None,\n", "                \"error\": None,\n", "            }\n", "\n", "        return {\n", "            \"query\": query,\n", "            \"parsed\": parsed,\n", "            \"retries\": retries,\n", "            \"sql_query\": None,\n", "            \"sql_results\": None,\n", "            \"error\": None,\n", "        }\n", "\n", "    except Exception as e:\n", "        print(f\"Error parsing: {e}\")\n", "        return {\n", "            \"query\": query,\n", "            \"parsed\": None,\n", "            \"retries\": retries + 1,\n", "            \"sql_query\": None,\n", "            \"sql_results\": None,\n", "            \"error\": str(e),\n", "        }\n", "\n", "\n", "def execute_sql_node(state: GraphState) -> GraphState:\n", "    parsed = state[\"parsed\"]\n", "\n", "    try:\n", "        schema_info = get_table_schema()\n", "        schema_text = (\n", "            \"\\n\".join([f\"{col[1]} ({col[2]})\" for col in schema_info])\n", "            if schema_info\n", "            else \"id, name, ...\"\n", "        )\n", "\n", "        formatted_prompt = sql_generation_prompt.format_messages(\n", "            schema=schema_text, parsed_data=parsed.model_dump_json()\n", "        )\n", "        response = llm.invoke(formatted_prompt)\n", "        sql_query = response.content.strip()\n", "\n", "        print(f\"🧠 LLM-Generated SQL:\\n{sql_query}\\n\")\n", "\n", "        results = execute_sql_query(sql_query)\n", "        print(f\"✅ Found {len(results)} matching candidates from SQL query\\n\")\n", "\n", "        return {\n", "            \"query\": state[\"query\"],\n", "            \"parsed\": parsed,\n", "            \"retries\": state.get(\"retries\", 0),\n", "            \"sql_query\": sql_query,\n", "            \"sql_results\": results,\n", "            \"error\": None,\n", "        }\n", "\n", "    except Exception as e:\n", "        error_msg = f\"SQL Error or Generation Failed: {str(e)}\"\n", "        print(f\"❌ {error_msg}\")\n", "        return {\n", "            \"query\": state[\"query\"],\n", "            \"parsed\": parsed,\n", "            \"retries\": state.get(\"retries\", 0),\n", "            \"sql_query\": None,\n", "            \"sql_results\": None,\n", "            \"error\": error_msg,\n", "        }\n", "\n", "\n", "def match_candidates_node(state: GraphState) -> GraphState:\n", "    query = state[\"query\"]\n", "    raw_candidates = state.get(\"sql_results\", [])\n", "\n", "    if not raw_candidates:\n", "        print(\"⚠️ No candidates to match.\")\n", "        return state\n", "\n", "    try:\n", "        print(\n", "            f\"🤖 Processing {len(raw_candidates)} candidates through matching engine...\"\n", "        )\n", "\n", "        formatted_prompt = matching_prompt.format(\n", "            user_query=query,\n", "            candidate_list=json.dumps(raw_candidates, ensure_ascii=False),\n", "        )\n", "\n", "        response = llm.invoke(formatted_prompt)\n", "\n", "        # Debug: Print raw response\n", "        # print(f\"🔍 Raw LLM Response (first 200 chars): {response.content[:200]}\")\n", "\n", "        # Clean the response content\n", "        response_content = response.content.strip()\n", "\n", "        # Handle cases where LLM might wrap JSON in markdown code blocks\n", "        if response_content.startswith(\"```json\"):\n", "            response_content = (\n", "                response_content.replace(\"```json\", \"\").replace(\"```\", \"\").strip()\n", "            )\n", "        elif response_content.startswith(\"```\"):\n", "            response_content = response_content.replace(\"```\", \"\").strip()\n", "\n", "        # Check if response is empty\n", "        if not response_content:\n", "            print(\"⚠️ LLM returned empty response. Using original candidates.\")\n", "            return {**state, \"sql_results\": raw_candidates}\n", "\n", "        # Try to parse JSON\n", "        try:\n", "            matched_results = json.loads(response_content)\n", "        except json.JSONDecodeError as json_error:\n", "            print(f\"❌ JSON Parse Error: {json_error}\")\n", "            print(f\"🔍 Problematic content: {response_content[:500]}\")\n", "            # Fallback: return original candidates\n", "            print(\"🔄 Falling back to original SQL results...\")\n", "            return {**state, \"sql_results\": raw_candidates}\n", "\n", "        # Validate that we got a list\n", "        if not isinstance(matched_results, list):\n", "            print(\n", "                f\"⚠️ Expected list, got {type(matched_results)}. Using original candidates.\"\n", "            )\n", "            return {**state, \"sql_results\": raw_candidates}\n", "\n", "        # print(f\"🏆 Matching complete! Found {len(matched_results)} qualified candidates\")\n", "\n", "        # if matched_results:\n", "        #     print(f\"📋 Top {min(3, len(matched_results))} candidates preview:\")\n", "        #     for i, candidate in enumerate(matched_results[:3], 1):\n", "        #         name = candidate.get('name', 'Unknown')\n", "        #         score = candidate.get('match_score', 0)\n", "        #         print(f\"  {i}. {name} (Score: {score})\")\n", "        # else:\n", "        #     print(\"⚠️ No candidates passed the matching criteria.\")\n", "\n", "        return {**state, \"sql_results\": matched_results}\n", "\n", "    except Exception as e:\n", "        print(f\"❌ Matching Error: {e}\")\n", "        print(f\"🔄 Falling back to original SQL results...\")\n", "        # Return original candidates as fallback\n", "        return {**state, \"sql_results\": raw_candidates}\n", "\n", "\n", "def result_node(state: GraphState) -> GraphState:\n", "    \"\"\"Shows SQL query results before matching\"\"\"\n", "    results = state.get(\"sql_results\", [])\n", "    if not results:\n", "        print(\"⚠️ No SQL results found.\")\n", "    else:\n", "        print(f\"✅ SQL Query returned {len(results)} candidates\")\n", "        # print(f\"📋 Sample candidates from SQL:\")\n", "        # for i, candidate in enumerate(results[:3], 1):\n", "        #     name = candidate.get(\"name\", \"Unknown\")\n", "        #     profession = candidate.get(\"profession\", \"N/A\")\n", "        #     experience = candidate.get(\"experience_years\", \"N/A\")\n", "        #     print(f\"  {i}. {name} - {profession} ({experience} years)\")\n", "        # print()\n", "    return state\n", "\n", "\n", "def final_result_node(state: GraphState) -> GraphState:\n", "    \"\"\"Shows final matched results after scoring\"\"\"\n", "    results = state.get(\"sql_results\", [])\n", "    if not results:\n", "        print(\"⚠️ No matching candidates found.\")\n", "    else:\n", "        print(f\"\\n📊 FINAL MATCHED RESULTS: {len(results)} candidates\")\n", "        # print(\"=\" * 60)\n", "        # # Show top candidates with scores if available\n", "        # for i, candidate in enumerate(results[:5], 1):\n", "        #     name = candidate.get(\"name\", \"Unknown\")\n", "        #     score = candidate.get(\"match_score\", \"N/A\")\n", "        #     profession = candidate.get(\"profession\", \"N/A\")\n", "        #     print(f\"  {i}. {name} - {profession} (Score: {score})\")\n", "        # print(\"=\" * 60)\n", "    return state\n", "\n", "\n", "def check_parsing_complete(state: GraphState) -> str:\n", "    if state[\"parsed\"] is not None:\n", "        return \"execute_sql\"\n", "    elif state[\"retries\"] >= 3:\n", "        print(\n", "            \"⚠️ Please search for a job title, skills, or relevant experience so I can assist you better!\"\n", "        )\n", "        return \"none\"\n", "    else:\n", "        return \"retry\"\n", "\n", "\n", "# ----------------------- 5. Build LangGraph (Updated Flow) -----------------------\n", "\n", "workflow = StateGraph(GraphState)\n", "\n", "workflow.add_node(\"parse_query\", parse_query_node)\n", "workflow.add_node(\"execute_sql\", execute_sql_node)\n", "workflow.add_node(\"sql_result\", result_node)\n", "workflow.add_node(\"match_candidates\", match_candidates_node)\n", "workflow.add_node(\"matching_result\", final_result_node)\n", "\n", "workflow.set_entry_point(\"parse_query\")\n", "workflow.add_edge(START, \"parse_query\")\n", "\n", "workflow.add_conditional_edges(\n", "    \"parse_query\",\n", "    check_parsing_complete,\n", "    {\"execute_sql\": \"execute_sql\", \"retry\": \"parse_query\", \"none\": END},\n", ")\n", "\n", "# Updated workflow: execute_sql -> result -> match_candidates -> final_result -> END\n", "workflow.add_edge(\"execute_sql\", \"sql_result\")\n", "workflow.add_edge(\"sql_result\", \"match_candidates\")\n", "workflow.add_edge(\"match_candidates\", \"matching_result\")\n", "workflow.add_edge(\"matching_result\", END)\n", "\n", "app = workflow.compile()\n", "\n", "# display(Image(app.get_graph().draw_mermaid_png(draw_method=MermaidDrawMethod.API)))\n", "\n", "\n", "# ----------------------- 6. Run a Test -----------------------\n", "\n", "query = \"who is donald trump?\"\n", "# query = \"who is donald trump\" # none will be returned\n", "print(f\"🚀 Starting search for: '{query}'\\n\")\n", "\n", "final_state = app.invoke({\"query\": query, \"parsed\": None, \"retries\": 0})\n", "\n", "# ----------------------- 7. Print Results in JSON -----------------------\n", "\n", "# print(\"\\n\" + \"=\" * 60)\n", "# print(\"EXECUTION SUMMARY\")\n", "# print(\"=\" * 60)\n", "print(f\"SQL Query: {final_state['sql_query']}\")\n", "\n", "results = final_state[\"sql_results\"]\n", "\n", "if results:\n", "    print(f\"Results Count: {len(results)}\")\n", "    print(\"\\n✅ Full Candidate Results in JSON:\\n\")\n", "    print(json.dumps(results, indent=2, ensure_ascii=False))\n", "\n", "    # Optional: save to file\n", "    # with open(\"matching_candidates.json\", \"w\", encoding=\"utf-8\") as f:\n", "    #     json.dump(results, f, indent=2, ensure_ascii=False)\n", "    #     print(\"📁 Results saved to 'matching_candidates.json'\")\n", "else:\n", "    print(\"❌ No results found or query failed.\")\n", "\n", "print(\"\\n🎉 Workflow completed successfully!\")\n"]}, {"cell_type": "markdown", "id": "d10e7c91", "metadata": {}, "source": ["## testing"]}, {"cell_type": "code", "execution_count": 26, "id": "81a4c701", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["mysql+mysqlconnector://rahul_ts_qa:nJdnRGG5OKOKmvTekci8rq2YGVdd%5Bj3K5t02qcuTQeUy6a7YgXqjTCbEIbVQHtTv@15.206.237.54:3306/candidate\n"]}], "source": ["# ----------------------- 0. Imports & Setup -----------------------\n", "\n", "import json\n", "import os\n", "import sys\n", "import urllib.parse\n", "import sqlite3\n", "from typing import Any, Dict, List, Optional, TypedDict, Union\n", "\n", "import nest_asyncio\n", "import pandas as pd\n", "from dotenv import load_dotenv\n", "from langchain.output_parsers import PydanticOutputParser\n", "from langchain.prompts import ChatPromptTemplate\n", "from langgraph.graph import END, StateGraph, START\n", "from langchain_openai import ChatOpenAI\n", "from langgraph.graph import StateGraph\n", "from loguru import logger\n", "from pydantic import BaseModel, Field\n", "from sqlalchemy import create_engine, text\n", "from IPython.display import display, Image\n", "from langchain_core.runnables.graph import MermaidDrawMethod\n", "\n", "load_dotenv()\n", "nest_asyncio.apply()\n", "\n", "# Logger Setup\n", "logger.remove()\n", "logger.add(sys.stdout, format=\"<level>{level}</level>: <cyan>{message}</cyan>\", colorize=True, level=\"DEBUG\")\n", "logger.level(\"DEBUG\", color=\"<blue>\")\n", "logger.level(\"INFO\", color=\"<green>\")\n", "logger.level(\"WARNING\", color=\"<yellow>\")\n", "logger.level(\"ERROR\", color=\"<red>\")\n", "logger.level(\"CRITICAL\", color=\"<RED><bold>\")\n", "\n", "# ----------------------- 1. <PERSON><PERSON><PERSON><PERSON> -----------------------\n", "\n", "class LanguageItem(BaseModel):\n", "    language: str = Field(..., description=\"The language spoken by the candidate\")\n", "    proficiency: Optional[str] = Field(None, description=\"Proficiency level (e.g., B2, C1)\")\n", "\n", "class ParsedQuery(BaseModel):\n", "    profession: Optional[str] = Field(None, description=\"Job title\")\n", "    # skills: Optional[List[str]] = Field(None, description=\"List of required skills\")\n", "    experience_years: Optional[Union[str, int]] = Field(None, description=\"Years of experience required\")\n", "    # education: Optional[str] = Field(None, description=\"Education requirement\")\n", "    languages: Optional[List[LanguageItem]] = Field(None, description=\"Languages + proficiency\")\n", "\n", "class GraphState(TypedDict):\n", "    query: str\n", "    parsed: Optional[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>]\n", "    retries: int\n", "    sql_query: Optional[str]\n", "    sql_results: Optional[List[Dict[str, Any]]]\n", "    error: Optional[str]\n", "\n", "# ----------------------- 2. Database Config -----------------------\n", "\n", "db_host = os.getenv(\"MYSQL_HOST\")\n", "db_port = os.getenv(\"MYSQL_PORT\")\n", "db_user = os.getenv(\"MYSQL_USER\")\n", "db_password = os.getenv(\"MYSQL_PASSWORD\")\n", "db_name = os.getenv(\"CANDIDATE_DATABASE\") \n", "\n", "db_url = f\"mysql+mysqlconnector://{db_user}:{urllib.parse.quote_plus(db_password)}@{db_host}:{db_port}/{db_name}\"\n", "print(db_url)"]}, {"cell_type": "code", "execution_count": 27, "id": "62e48faf", "metadata": {}, "outputs": [], "source": ["\n", "engine = create_engine(db_url)\n", "\n", "def execute_stored_procedure(params: Dict[str, Any]) -> List[Dict[str, Any]]:\n", "    try:\n", "        with engine.connect() as conn:\n", "            result = conn.execute(\n", "                text(\"CALL sp_filter_candidate_for_sureai(:keyword, :years, :language, :proficiency, :lang_code)\"),\n", "                params\n", "            )\n", "            rows = result.fetchall()\n", "            columns = result.keys()\n", "            return [dict(zip(columns, row)) for row in rows]\n", "    except Exception as e:\n", "        logger.error(f\"Stored Procedure Execution Error: {e}\")\n", "        return []\n", "\n", "# ----------------------- 3. LLM & Prompt Setup -----------------------\n", "\n", "api_key = os.getenv(\"OPENAI_API_KEY\")\n", "llm = ChatOpenAI(model=\"gpt-4.1-mini\", temperature=0, api_key=api_key)\n", "\n", "parser = PydanticOutputParser(pydantic_object=ParsedQuery)\n", "format_instructions = parser.get_format_instructions()\n", "\n", "prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\"system\", \"\"\"You are a recruitment assistant. You will receive natural language queries from employers describing the kind of candidates they are looking for.\n", "\n", "Your job is to extract:\n", "- profession\n", "- skills\n", "- experience_years\n", "- education\n", "- languages (with proficiency)\n", "\n", "If a field is not mentioned, leave it null. Be strict and output exactly in this format: {format_instructions}\"\"\"),\n", "        (\"user\", \"{query}\\n\\n{format_instructions}\"),\n", "    ]\n", ")\n", "\n", "# ----------------------- 4. LangG<PERSON><PERSON>des -----------------------\n", "\n", "def parse_query_node(state: GraphState) -> GraphState:\n", "    query = state[\"query\"]\n", "    retries = state.get(\"retries\", 0)\n", "\n", "    try:\n", "        formatted_prompt = prompt.format_messages(query=query, format_instructions=format_instructions)\n", "        response = llm.invoke(formatted_prompt)\n", "        parsed = parser.parse(response.content)\n", "\n", "        print(f\"🔍 Attempt {retries+1} | Parsed:\\n{parsed}\\n\")\n", "\n", "        # if not any([parsed.profession, parsed.skills, parsed.experience_years, parsed.education, parsed.languages]):\n", "        #     return {**state, \"parsed\": None, \"retries\": retries + 1}\n", "        if not any([parsed.profession,  parsed.experience_years, parsed.languages]):\n", "            return {**state, \"parsed\": None, \"retries\": retries + 1}\n", "        return {**state, \"parsed\": parsed, \"retries\": retries}\n", "\n", "    except Exception as e:\n", "        print(f\"❌ Error parsing: {e}\")\n", "        return {**state, \"parsed\": None, \"retries\": retries + 1, \"error\": str(e)}\n", "\n", "def run_stored_procedure_node(state: GraphState) -> GraphState:\n", "    parsed = state.get(\"parsed\")\n", "    if not parsed:\n", "        print(\"❌ No parsed query to run stored procedure.\")\n", "        return state\n", "\n", "    keyword = parsed.profession or ''\n", "    years = int(parsed.experience_years) if parsed.experience_years else 0\n", "    language = parsed.languages[0].language if parsed.languages else ''\n", "    proficiency = parsed.languages[0].proficiency if parsed.languages and parsed.languages[0].proficiency else ''\n", "    lang_code = 'en'\n", "\n", "    params = {\n", "        \"keyword\": keyword,\n", "        \"years\": years,\n", "        \"language\": language,\n", "        \"proficiency\": proficiency,\n", "        \"lang_code\": lang_code,\n", "    }\n", "\n", "    results = execute_stored_procedure(params)\n", "\n", "    if results:\n", "        df = pd.DataFrame(results)\n", "        print(\"📊 Top Candidates:\")\n", "        display(df.head(5))\n", "    else:\n", "        print(\"⚠️ No candidates found.\")\n", "\n", "    return {**state, \"sql_results\": results, \"error\": None}\n", "\n", "def result_node(state: GraphState) -> GraphState:\n", "    results = state.get(\"sql_results\", [])\n", "    print(f\"✅ Query returned {len(results)} results\" if results else \"⚠️ No results to show.\")\n", "    return state\n", "\n", "# ----------------------- 5. LangGraph Workflow -----------------------\n", "\n", "workflow = StateGraph(GraphState)\n", "workflow.add_node(\"parse_query\", parse_query_node)\n", "workflow.add_node(\"run_stored_procedure\", run_stored_procedure_node)\n", "workflow.add_node(\"result\", result_node)\n", "\n", "workflow.set_entry_point(\"parse_query\")\n", "workflow.add_edge(\"parse_query\", \"run_stored_procedure\")\n", "workflow.add_edge(\"run_stored_procedure\", \"result\")\n", "workflow.add_edge(\"result\", END)\n", "\n", "app = workflow.compile()"]}, {"cell_type": "code", "execution_count": 28, "id": "802e0e22", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(Image(app.get_graph().draw_mermaid_png(draw_method=MermaidDrawMethod.API)))"]}, {"cell_type": "code", "execution_count": 25, "id": "aab277d5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🚀 Starting search for: 'Find me a d with 2 years of experience'\n", "\n", "🔍 Attempt 1 | Parsed:\n", "profession='d' experience_years=2\n", "\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mKeyboardInterrupt\u001b[39m                         <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[25]\u001b[39m\u001b[32m, line 16\u001b[39m\n\u001b[32m      5\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[33m🚀 Starting search for: \u001b[39m\u001b[33m'\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mquery\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m'\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[33m\"\u001b[39m)\n\u001b[32m      7\u001b[39m initial_state = {\n\u001b[32m      8\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mquery\u001b[39m\u001b[33m\"\u001b[39m: query,\n\u001b[32m      9\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mparsed\u001b[39m\u001b[33m\"\u001b[39m: \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[32m   (...)\u001b[39m\u001b[32m     13\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33merror\u001b[39m\u001b[33m\"\u001b[39m: \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m,\n\u001b[32m     14\u001b[39m }\n\u001b[32m---> \u001b[39m\u001b[32m16\u001b[39m final_state = \u001b[43mapp\u001b[49m\u001b[43m.\u001b[49m\u001b[43minvoke\u001b[49m\u001b[43m(\u001b[49m\u001b[43minitial_state\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     18\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[33m📦 Final State:\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m     19\u001b[39m \u001b[38;5;28mprint\u001b[39m(json.dumps(final_state, indent=\u001b[32m2\u001b[39m, default=\u001b[38;5;28mstr\u001b[39m))\n", "\u001b[36mFile \u001b[39m\u001b[32m~/uv_environments/testing/lib/python3.12/site-packages/langgraph/pregel/__init__.py:2823\u001b[39m, in \u001b[36mPregel.invoke\u001b[39m\u001b[34m(self, input, config, stream_mode, output_keys, interrupt_before, interrupt_after, checkpoint_during, debug, **kwargs)\u001b[39m\n\u001b[32m   2820\u001b[39m chunks: \u001b[38;5;28mlist\u001b[39m[Union[\u001b[38;5;28mdict\u001b[39m[\u001b[38;5;28mstr\u001b[39m, Any], Any]] = []\n\u001b[32m   2821\u001b[39m interrupts: \u001b[38;5;28mlist\u001b[39m[Interrupt] = []\n\u001b[32m-> \u001b[39m\u001b[32m2823\u001b[39m \u001b[43m\u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mchunk\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mstream\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   2824\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43minput\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m   2825\u001b[39m \u001b[43m    \u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2826\u001b[39m \u001b[43m    \u001b[49m\u001b[43mstream_mode\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream_mode\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2827\u001b[39m \u001b[43m    \u001b[49m\u001b[43moutput_keys\u001b[49m\u001b[43m=\u001b[49m\u001b[43moutput_keys\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2828\u001b[39m \u001b[43m    \u001b[49m\u001b[43minterrupt_before\u001b[49m\u001b[43m=\u001b[49m\u001b[43minterrupt_before\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2829\u001b[39m \u001b[43m    \u001b[49m\u001b[43minterrupt_after\u001b[49m\u001b[43m=\u001b[49m\u001b[43minterrupt_after\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2830\u001b[39m \u001b[43m    \u001b[49m\u001b[43mcheckpoint_during\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcheckpoint_during\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2831\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdebug\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdebug\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2832\u001b[39m \u001b[43m    \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2833\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m   2834\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mstream_mode\u001b[49m\u001b[43m \u001b[49m\u001b[43m==\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mvalues\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\n\u001b[32m   2835\u001b[39m \u001b[43m        \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   2836\u001b[39m \u001b[43m            \u001b[49m\u001b[38;5;28;43misinstance\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mchunk\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mdict\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m   2837\u001b[39m \u001b[43m            \u001b[49m\u001b[38;5;129;43;01mand\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43m(\u001b[49m\u001b[43mints\u001b[49m\u001b[43m \u001b[49m\u001b[43m:=\u001b[49m\u001b[43m \u001b[49m\u001b[43mchunk\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[43mINTERRUPT\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mis\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mnot\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\n\u001b[32m   2838\u001b[39m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/uv_environments/testing/lib/python3.12/site-packages/langgraph/pregel/__init__.py:2461\u001b[39m, in \u001b[36mPregel.stream\u001b[39m\u001b[34m(self, input, config, stream_mode, output_keys, interrupt_before, interrupt_after, checkpoint_during, debug, subgraphs)\u001b[39m\n\u001b[32m   2455\u001b[39m     \u001b[38;5;66;03m# Similarly to Bulk Synchronous Parallel / Pregel model\u001b[39;00m\n\u001b[32m   2456\u001b[39m     \u001b[38;5;66;03m# computation proceeds in steps, while there are channel updates.\u001b[39;00m\n\u001b[32m   2457\u001b[39m     \u001b[38;5;66;03m# Channel updates from step N are only visible in step N+1\u001b[39;00m\n\u001b[32m   2458\u001b[39m     \u001b[38;5;66;03m# channels are guaranteed to be immutable for the duration of the step,\u001b[39;00m\n\u001b[32m   2459\u001b[39m     \u001b[38;5;66;03m# with channel updates applied only at the transition between steps.\u001b[39;00m\n\u001b[32m   2460\u001b[39m     \u001b[38;5;28;01mwhile\u001b[39;00m loop.tick(input_keys=\u001b[38;5;28mself\u001b[39m.input_channels):\n\u001b[32m-> \u001b[39m\u001b[32m2461\u001b[39m \u001b[43m        \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43m_\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mrunner\u001b[49m\u001b[43m.\u001b[49m\u001b[43mtick\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   2462\u001b[39m \u001b[43m            \u001b[49m\u001b[43mloop\u001b[49m\u001b[43m.\u001b[49m\u001b[43mtasks\u001b[49m\u001b[43m.\u001b[49m\u001b[43mvalues\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2463\u001b[39m \u001b[43m            \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mstep_timeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2464\u001b[39m \u001b[43m            \u001b[49m\u001b[43mretry_policy\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mretry_policy\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2465\u001b[39m \u001b[43m            \u001b[49m\u001b[43mget_waiter\u001b[49m\u001b[43m=\u001b[49m\u001b[43mget_waiter\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2466\u001b[39m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m   2467\u001b[39m \u001b[43m            \u001b[49m\u001b[38;5;66;43;03m# emit output\u001b[39;49;00m\n\u001b[32m   2468\u001b[39m \u001b[43m            \u001b[49m\u001b[38;5;28;43;01myield from\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43moutput\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   2469\u001b[39m \u001b[38;5;66;03m# emit output\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/uv_environments/testing/lib/python3.12/site-packages/langgraph/pregel/runner.py:153\u001b[39m, in \u001b[36mPregelRunner.tick\u001b[39m\u001b[34m(self, tasks, reraise, timeout, retry_policy, get_waiter)\u001b[39m\n\u001b[32m    151\u001b[39m t = tasks[\u001b[32m0\u001b[39m]\n\u001b[32m    152\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m153\u001b[39m     \u001b[43mrun_with_retry\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    154\u001b[39m \u001b[43m        \u001b[49m\u001b[43mt\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    155\u001b[39m \u001b[43m        \u001b[49m\u001b[43mretry_policy\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    156\u001b[39m \u001b[43m        \u001b[49m\u001b[43mconfigurable\u001b[49m\u001b[43m=\u001b[49m\u001b[43m{\u001b[49m\n\u001b[32m    157\u001b[39m \u001b[43m            \u001b[49m\u001b[43mCONFIG_KEY_CALL\u001b[49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mpartial\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    158\u001b[39m \u001b[43m                \u001b[49m\u001b[43m_call\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    159\u001b[39m \u001b[43m                \u001b[49m\u001b[43mweakref\u001b[49m\u001b[43m.\u001b[49m\u001b[43mref\u001b[49m\u001b[43m(\u001b[49m\u001b[43mt\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    160\u001b[39m \u001b[43m                \u001b[49m\u001b[43mretry\u001b[49m\u001b[43m=\u001b[49m\u001b[43mretry_policy\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    161\u001b[39m \u001b[43m                \u001b[49m\u001b[43mfutures\u001b[49m\u001b[43m=\u001b[49m\u001b[43mweakref\u001b[49m\u001b[43m.\u001b[49m\u001b[43mref\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfutures\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    162\u001b[39m \u001b[43m                \u001b[49m\u001b[43mschedule_task\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mschedule_task\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    163\u001b[39m \u001b[43m                \u001b[49m\u001b[43msubmit\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43msubmit\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    164\u001b[39m \u001b[43m                \u001b[49m\u001b[43mreraise\u001b[49m\u001b[43m=\u001b[49m\u001b[43mreraise\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    165\u001b[39m \u001b[43m            \u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    166\u001b[39m \u001b[43m        \u001b[49m\u001b[43m}\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    167\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    168\u001b[39m     \u001b[38;5;28mself\u001b[39m.commit(t, \u001b[38;5;28;01mNone\u001b[39;00m)\n\u001b[32m    169\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m exc:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/uv_environments/testing/lib/python3.12/site-packages/langgraph/pregel/retry.py:40\u001b[39m, in \u001b[36mrun_with_retry\u001b[39m\u001b[34m(task, retry_policy, configurable)\u001b[39m\n\u001b[32m     38\u001b[39m     task.writes.clear()\n\u001b[32m     39\u001b[39m     \u001b[38;5;66;03m# run the task\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m40\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mtask\u001b[49m\u001b[43m.\u001b[49m\u001b[43mproc\u001b[49m\u001b[43m.\u001b[49m\u001b[43minvoke\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtask\u001b[49m\u001b[43m.\u001b[49m\u001b[43minput\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     41\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m ParentCommand \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[32m     42\u001b[39m     ns: \u001b[38;5;28mstr\u001b[39m = config[CONF][CONFIG_KEY_CHECKPOINT_NS]\n", "\u001b[36mFile \u001b[39m\u001b[32m~/uv_environments/testing/lib/python3.12/site-packages/langgraph/utils/runnable.py:623\u001b[39m, in \u001b[36mRunnableSeq.invoke\u001b[39m\u001b[34m(self, input, config, **kwargs)\u001b[39m\n\u001b[32m    621\u001b[39m     \u001b[38;5;66;03m# run in context\u001b[39;00m\n\u001b[32m    622\u001b[39m     \u001b[38;5;28;01mwith\u001b[39;00m set_config_context(config, run) \u001b[38;5;28;01mas\u001b[39;00m context:\n\u001b[32m--> \u001b[39m\u001b[32m623\u001b[39m         \u001b[38;5;28minput\u001b[39m = \u001b[43mcontext\u001b[49m\u001b[43m.\u001b[49m\u001b[43mrun\u001b[49m\u001b[43m(\u001b[49m\u001b[43mstep\u001b[49m\u001b[43m.\u001b[49m\u001b[43minvoke\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43minput\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    624\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m    625\u001b[39m     \u001b[38;5;28minput\u001b[39m = step.invoke(\u001b[38;5;28minput\u001b[39m, config)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/uv_environments/testing/lib/python3.12/site-packages/langgraph/utils/runnable.py:377\u001b[39m, in \u001b[36mRunnableCallable.invoke\u001b[39m\u001b[34m(self, input, config, **kwargs)\u001b[39m\n\u001b[32m    375\u001b[39m         run_manager.on_chain_end(ret)\n\u001b[32m    376\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m377\u001b[39m     ret = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    378\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m.recurse \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(ret, Runnable):\n\u001b[32m    379\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m ret.invoke(\u001b[38;5;28minput\u001b[39m, config)\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[21]\u001b[39m\u001b[32m, line 84\u001b[39m, in \u001b[36mrun_stored_procedure_node\u001b[39m\u001b[34m(state)\u001b[39m\n\u001b[32m     74\u001b[39m lang_code = \u001b[33m'\u001b[39m\u001b[33men\u001b[39m\u001b[33m'\u001b[39m\n\u001b[32m     76\u001b[39m params = {\n\u001b[32m     77\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mkeyword\u001b[39m\u001b[33m\"\u001b[39m: keyword,\n\u001b[32m     78\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33myears\u001b[39m\u001b[33m\"\u001b[39m: years,\n\u001b[32m   (...)\u001b[39m\u001b[32m     81\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mlang_code\u001b[39m\u001b[33m\"\u001b[39m: lang_code,\n\u001b[32m     82\u001b[39m }\n\u001b[32m---> \u001b[39m\u001b[32m84\u001b[39m results = \u001b[43mexecute_stored_procedure\u001b[49m\u001b[43m(\u001b[49m\u001b[43mparams\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     86\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m results:\n\u001b[32m     87\u001b[39m     df = pd.<PERSON><PERSON><PERSON><PERSON>(results)\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[21]\u001b[39m\u001b[32m, line 5\u001b[39m, in \u001b[36mexecute_stored_procedure\u001b[39m\u001b[34m(params)\u001b[39m\n\u001b[32m      3\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mexecute_stored_procedure\u001b[39m(params: Dict[\u001b[38;5;28mstr\u001b[39m, Any]) -> List[Dict[\u001b[38;5;28mstr\u001b[39m, Any]]:\n\u001b[32m      4\u001b[39m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m----> \u001b[39m\u001b[32m5\u001b[39m         \u001b[38;5;28;01mwith\u001b[39;00m \u001b[43mengine\u001b[49m\u001b[43m.\u001b[49m\u001b[43mconnect\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m \u001b[38;5;28;01mas\u001b[39;00m conn:\n\u001b[32m      6\u001b[39m             result = conn.execute(\n\u001b[32m      7\u001b[39m                 text(\u001b[33m\"\u001b[39m\u001b[33mCALL sp_filter_candidate_for_sureai(:keyword, :years, :language, :proficiency, :lang_code)\u001b[39m\u001b[33m\"\u001b[39m),\n\u001b[32m      8\u001b[39m                 params\n\u001b[32m      9\u001b[39m             )\n\u001b[32m     10\u001b[39m             rows = result.fetchall()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/uv_environments/testing/lib/python3.12/site-packages/sqlalchemy/engine/base.py:3325\u001b[39m, in \u001b[36mEngine.connect\u001b[39m\u001b[34m(self, close_with_result)\u001b[39m\n\u001b[32m   3310\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mconnect\u001b[39m(\u001b[38;5;28mself\u001b[39m, close_with_result=\u001b[38;5;28;01mFalse\u001b[39;00m):\n\u001b[32m   3311\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"Return a new :class:`_engine.Connection` object.\u001b[39;00m\n\u001b[32m   3312\u001b[39m \n\u001b[32m   3313\u001b[39m \u001b[33;03m    The :class:`_engine.Connection` object is a facade that uses a DBAPI\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m   3322\u001b[39m \n\u001b[32m   3323\u001b[39m \u001b[33;03m    \"\"\"\u001b[39;00m\n\u001b[32m-> \u001b[39m\u001b[32m3325\u001b[39m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_connection_cls\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mclose_with_result\u001b[49m\u001b[43m=\u001b[49m\u001b[43mclose_with_result\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/uv_environments/testing/lib/python3.12/site-packages/sqlalchemy/engine/base.py:96\u001b[39m, in \u001b[36mConnection.__init__\u001b[39m\u001b[34m(self, engine, connection, close_with_result, _branch_from, _execution_options, _dispatch, _has_events, _allow_revalidate)\u001b[39m\n\u001b[32m     91\u001b[39m     \u001b[38;5;28mself\u001b[39m._has_events = _branch_from._has_events\n\u001b[32m     92\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m     93\u001b[39m     \u001b[38;5;28mself\u001b[39m._dbapi_connection = (\n\u001b[32m     94\u001b[39m         connection\n\u001b[32m     95\u001b[39m         \u001b[38;5;28;01mif\u001b[39;00m connection \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m96\u001b[39m         \u001b[38;5;28;01melse\u001b[39;00m \u001b[43mengine\u001b[49m\u001b[43m.\u001b[49m\u001b[43mraw_connection\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     97\u001b[39m     )\n\u001b[32m     99\u001b[39m     \u001b[38;5;28mself\u001b[39m._transaction = \u001b[38;5;28mself\u001b[39m._nested_transaction = \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m    100\u001b[39m     \u001b[38;5;28mself\u001b[39m.__savepoint_seq = \u001b[32m0\u001b[39m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/uv_environments/testing/lib/python3.12/site-packages/sqlalchemy/engine/base.py:3404\u001b[39m, in \u001b[36mEngine.raw_connection\u001b[39m\u001b[34m(self, _connection)\u001b[39m\n\u001b[32m   3382\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mraw_connection\u001b[39m(\u001b[38;5;28mself\u001b[39m, _connection=\u001b[38;5;28;01mNone\u001b[39;00m):\n\u001b[32m   3383\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"Return a \"raw\" DBAPI connection from the connection pool.\u001b[39;00m\n\u001b[32m   3384\u001b[39m \n\u001b[32m   3385\u001b[39m \u001b[33;03m    The returned object is a proxied version of the DBAPI\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m   3402\u001b[39m \n\u001b[32m   3403\u001b[39m \u001b[33;03m    \"\"\"\u001b[39;00m\n\u001b[32m-> \u001b[39m\u001b[32m3404\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_wrap_pool_connect\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mpool\u001b[49m\u001b[43m.\u001b[49m\u001b[43mconnect\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m_connection\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/uv_environments/testing/lib/python3.12/site-packages/sqlalchemy/engine/base.py:3371\u001b[39m, in \u001b[36mEngine._wrap_pool_connect\u001b[39m\u001b[34m(self, fn, connection)\u001b[39m\n\u001b[32m   3369\u001b[39m dialect = \u001b[38;5;28mself\u001b[39m.dialect\n\u001b[32m   3370\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m3371\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfn\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   3372\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m dialect.dbapi.Error \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[32m   3373\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m connection \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/uv_environments/testing/lib/python3.12/site-packages/sqlalchemy/pool/base.py:327\u001b[39m, in \u001b[36mPool.connect\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    319\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mconnect\u001b[39m(\u001b[38;5;28mself\u001b[39m):\n\u001b[32m    320\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"Return a DBAPI connection from the pool.\u001b[39;00m\n\u001b[32m    321\u001b[39m \n\u001b[32m    322\u001b[39m \u001b[33;03m    The connection is instrumented such that when its\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m    325\u001b[39m \n\u001b[32m    326\u001b[39m \u001b[33;03m    \"\"\"\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m327\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_ConnectionFairy\u001b[49m\u001b[43m.\u001b[49m\u001b[43m_checkout\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/uv_environments/testing/lib/python3.12/site-packages/sqlalchemy/pool/base.py:894\u001b[39m, in \u001b[36m_ConnectionFairy._checkout\u001b[39m\u001b[34m(cls, pool, threadconns, fairy)\u001b[39m\n\u001b[32m    891\u001b[39m \u001b[38;5;129m@classmethod\u001b[39m\n\u001b[32m    892\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m_checkout\u001b[39m(\u001b[38;5;28mcls\u001b[39m, pool, threadconns=\u001b[38;5;28;01mNone\u001b[39;00m, fairy=\u001b[38;5;28;01mNone\u001b[39;00m):\n\u001b[32m    893\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m fairy:\n\u001b[32m--> \u001b[39m\u001b[32m894\u001b[39m         fairy = \u001b[43m_ConnectionRecord\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcheckout\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpool\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    896\u001b[39m         fairy._pool = pool\n\u001b[32m    897\u001b[39m         fairy._counter = \u001b[32m0\u001b[39m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/uv_environments/testing/lib/python3.12/site-packages/sqlalchemy/pool/base.py:493\u001b[39m, in \u001b[36m_ConnectionRecord.checkout\u001b[39m\u001b[34m(cls, pool)\u001b[39m\n\u001b[32m    491\u001b[39m \u001b[38;5;129m@classmethod\u001b[39m\n\u001b[32m    492\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mcheckout\u001b[39m(\u001b[38;5;28mcls\u001b[39m, pool):\n\u001b[32m--> \u001b[39m\u001b[32m493\u001b[39m     rec = \u001b[43mpool\u001b[49m\u001b[43m.\u001b[49m\u001b[43m_do_get\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    494\u001b[39m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m    495\u001b[39m         dbapi_connection = rec.get_connection()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/uv_environments/testing/lib/python3.12/site-packages/sqlalchemy/pool/impl.py:145\u001b[39m, in \u001b[36mQueuePool._do_get\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    143\u001b[39m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m._create_connection()\n\u001b[32m    144\u001b[39m     \u001b[38;5;28;01mexcept\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m145\u001b[39m \u001b[43m        \u001b[49m\u001b[38;5;28;43;01mwith\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mutil\u001b[49m\u001b[43m.\u001b[49m\u001b[43msafe_reraise\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m    146\u001b[39m \u001b[43m            \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_dec_overflow\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    147\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/uv_environments/testing/lib/python3.12/site-packages/sqlalchemy/util/langhelpers.py:70\u001b[39m, in \u001b[36msafe_reraise.__exit__\u001b[39m\u001b[34m(self, type_, value, traceback)\u001b[39m\n\u001b[32m     68\u001b[39m     \u001b[38;5;28mself\u001b[39m._exc_info = \u001b[38;5;28;01mNone\u001b[39;00m  \u001b[38;5;66;03m# remove potential circular references\u001b[39;00m\n\u001b[32m     69\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m.warn_only:\n\u001b[32m---> \u001b[39m\u001b[32m70\u001b[39m         \u001b[43mcompat\u001b[49m\u001b[43m.\u001b[49m\u001b[43mraise_\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m     71\u001b[39m \u001b[43m            \u001b[49m\u001b[43mexc_value\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     72\u001b[39m \u001b[43m            \u001b[49m\u001b[43mwith_traceback\u001b[49m\u001b[43m=\u001b[49m\u001b[43mexc_tb\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     73\u001b[39m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     74\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m     75\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m compat.py3k \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mself\u001b[39m._exc_info \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mself\u001b[39m._exc_info[\u001b[32m1\u001b[39m]:\n\u001b[32m     76\u001b[39m         \u001b[38;5;66;03m# emulate Py3K's behavior of telling us when an exception\u001b[39;00m\n\u001b[32m     77\u001b[39m         \u001b[38;5;66;03m# occurs in an exception handler.\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/uv_environments/testing/lib/python3.12/site-packages/sqlalchemy/util/compat.py:211\u001b[39m, in \u001b[36mraise_\u001b[39m\u001b[34m(***failed resolving arguments***)\u001b[39m\n\u001b[32m    208\u001b[39m     exception.__cause__ = replace_context\n\u001b[32m    210\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m211\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m exception\n\u001b[32m    212\u001b[39m \u001b[38;5;28;01mfinally\u001b[39;00m:\n\u001b[32m    213\u001b[39m     \u001b[38;5;66;03m# credit to\u001b[39;00m\n\u001b[32m    214\u001b[39m     \u001b[38;5;66;03m# https://cosmicpercolator.com/2016/01/13/exception-leaks-in-python-2-and-3/\u001b[39;00m\n\u001b[32m    215\u001b[39m     \u001b[38;5;66;03m# as the __traceback__ object creates a cycle\u001b[39;00m\n\u001b[32m    216\u001b[39m     \u001b[38;5;28;01mdel\u001b[39;00m exception, replace_context, from_, with_traceback\n", "\u001b[36mFile \u001b[39m\u001b[32m~/uv_environments/testing/lib/python3.12/site-packages/sqlalchemy/pool/impl.py:143\u001b[39m, in \u001b[36mQueuePool._do_get\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    141\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m._inc_overflow():\n\u001b[32m    142\u001b[39m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m143\u001b[39m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_create_connection\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    144\u001b[39m     \u001b[38;5;28;01mexcept\u001b[39;00m:\n\u001b[32m    145\u001b[39m         \u001b[38;5;28;01mwith\u001b[39;00m util.safe_reraise():\n", "\u001b[36mFile \u001b[39m\u001b[32m~/uv_environments/testing/lib/python3.12/site-packages/sqlalchemy/pool/base.py:273\u001b[39m, in \u001b[36mPool._create_connection\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    270\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m_create_connection\u001b[39m(\u001b[38;5;28mself\u001b[39m):\n\u001b[32m    271\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"Called by subclasses to create a new ConnectionRecord.\"\"\"\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m273\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_ConnectionRecord\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/uv_environments/testing/lib/python3.12/site-packages/sqlalchemy/pool/base.py:388\u001b[39m, in \u001b[36m_ConnectionRecord.__init__\u001b[39m\u001b[34m(self, pool, connect)\u001b[39m\n\u001b[32m    386\u001b[39m \u001b[38;5;28mself\u001b[39m.__pool = pool\n\u001b[32m    387\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m connect:\n\u001b[32m--> \u001b[39m\u001b[32m388\u001b[39m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m__connect\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    389\u001b[39m \u001b[38;5;28mself\u001b[39m.finalize_callback = deque()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/uv_environments/testing/lib/python3.12/site-packages/sqlalchemy/pool/base.py:690\u001b[39m, in \u001b[36m_ConnectionRecord.__connect\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    688\u001b[39m     \u001b[38;5;28mself\u001b[39m.fresh = \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[32m    689\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mBaseException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[32m--> \u001b[39m\u001b[32m690\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01mwith\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mutil\u001b[49m\u001b[43m.\u001b[49m\u001b[43msafe_reraise\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m    691\u001b[39m \u001b[43m        \u001b[49m\u001b[43mpool\u001b[49m\u001b[43m.\u001b[49m\u001b[43mlogger\u001b[49m\u001b[43m.\u001b[49m\u001b[43mdebug\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mError on connect(): \u001b[39;49m\u001b[38;5;132;43;01m%s\u001b[39;49;00m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43me\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    692\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m    693\u001b[39m     \u001b[38;5;66;03m# in SQLAlchemy 1.4 the first_connect event is not used by\u001b[39;00m\n\u001b[32m    694\u001b[39m     \u001b[38;5;66;03m# the engine, so this will usually not be set\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/uv_environments/testing/lib/python3.12/site-packages/sqlalchemy/util/langhelpers.py:70\u001b[39m, in \u001b[36msafe_reraise.__exit__\u001b[39m\u001b[34m(self, type_, value, traceback)\u001b[39m\n\u001b[32m     68\u001b[39m     \u001b[38;5;28mself\u001b[39m._exc_info = \u001b[38;5;28;01mNone\u001b[39;00m  \u001b[38;5;66;03m# remove potential circular references\u001b[39;00m\n\u001b[32m     69\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m.warn_only:\n\u001b[32m---> \u001b[39m\u001b[32m70\u001b[39m         \u001b[43mcompat\u001b[49m\u001b[43m.\u001b[49m\u001b[43mraise_\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m     71\u001b[39m \u001b[43m            \u001b[49m\u001b[43mexc_value\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     72\u001b[39m \u001b[43m            \u001b[49m\u001b[43mwith_traceback\u001b[49m\u001b[43m=\u001b[49m\u001b[43mexc_tb\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     73\u001b[39m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     74\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m     75\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m compat.py3k \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mself\u001b[39m._exc_info \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mself\u001b[39m._exc_info[\u001b[32m1\u001b[39m]:\n\u001b[32m     76\u001b[39m         \u001b[38;5;66;03m# emulate Py3K's behavior of telling us when an exception\u001b[39;00m\n\u001b[32m     77\u001b[39m         \u001b[38;5;66;03m# occurs in an exception handler.\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/uv_environments/testing/lib/python3.12/site-packages/sqlalchemy/util/compat.py:211\u001b[39m, in \u001b[36mraise_\u001b[39m\u001b[34m(***failed resolving arguments***)\u001b[39m\n\u001b[32m    208\u001b[39m     exception.__cause__ = replace_context\n\u001b[32m    210\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m211\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m exception\n\u001b[32m    212\u001b[39m \u001b[38;5;28;01mfinally\u001b[39;00m:\n\u001b[32m    213\u001b[39m     \u001b[38;5;66;03m# credit to\u001b[39;00m\n\u001b[32m    214\u001b[39m     \u001b[38;5;66;03m# https://cosmicpercolator.com/2016/01/13/exception-leaks-in-python-2-and-3/\u001b[39;00m\n\u001b[32m    215\u001b[39m     \u001b[38;5;66;03m# as the __traceback__ object creates a cycle\u001b[39;00m\n\u001b[32m    216\u001b[39m     \u001b[38;5;28;01mdel\u001b[39;00m exception, replace_context, from_, with_traceback\n", "\u001b[36mFile \u001b[39m\u001b[32m~/uv_environments/testing/lib/python3.12/site-packages/sqlalchemy/pool/base.py:686\u001b[39m, in \u001b[36m_ConnectionRecord.__connect\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    684\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m    685\u001b[39m     \u001b[38;5;28mself\u001b[39m.starttime = time.time()\n\u001b[32m--> \u001b[39m\u001b[32m686\u001b[39m     \u001b[38;5;28mself\u001b[39m.dbapi_connection = connection = \u001b[43mpool\u001b[49m\u001b[43m.\u001b[49m\u001b[43m_invoke_creator\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m    687\u001b[39m     pool.logger.debug(\u001b[33m\"\u001b[39m\u001b[33mCreated new connection \u001b[39m\u001b[38;5;132;01m%r\u001b[39;00m\u001b[33m\"\u001b[39m, connection)\n\u001b[32m    688\u001b[39m     \u001b[38;5;28mself\u001b[39m.fresh = \u001b[38;5;28;01mTrue\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/uv_environments/testing/lib/python3.12/site-packages/sqlalchemy/engine/create.py:574\u001b[39m, in \u001b[36mcreate_engine.<locals>.connect\u001b[39m\u001b[34m(connection_record)\u001b[39m\n\u001b[32m    572\u001b[39m         \u001b[38;5;28;01mif\u001b[39;00m connection \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m    573\u001b[39m             \u001b[38;5;28;01mreturn\u001b[39;00m connection\n\u001b[32m--> \u001b[39m\u001b[32m574\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mdialect\u001b[49m\u001b[43m.\u001b[49m\u001b[43mconnect\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43mcargs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mcparams\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/uv_environments/testing/lib/python3.12/site-packages/sqlalchemy/engine/default.py:598\u001b[39m, in \u001b[36mDefaultDialect.connect\u001b[39m\u001b[34m(self, *cargs, **cparams)\u001b[39m\n\u001b[32m    596\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mconnect\u001b[39m(\u001b[38;5;28mself\u001b[39m, *cargs, **cparams):\n\u001b[32m    597\u001b[39m     \u001b[38;5;66;03m# inherits the docstring from interfaces.Dialect.connect\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m598\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mdbapi\u001b[49m\u001b[43m.\u001b[49m\u001b[43mconnect\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43mcargs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mcparams\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/uv_environments/testing/lib/python3.12/site-packages/mysql/connector/pooling.py:323\u001b[39m, in \u001b[36mconnect\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m    321\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m CMySQLConnection \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m use_pure:\n\u001b[32m    322\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m CMySQLConnection(*args, **kwargs)\n\u001b[32m--> \u001b[39m\u001b[32m323\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mMySQLConnection\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/uv_environments/testing/lib/python3.12/site-packages/mysql/connector/connection.py:185\u001b[39m, in \u001b[36mMySQLConnection.__init__\u001b[39m\u001b[34m(self, **kwargs)\u001b[39m\n\u001b[32m    183\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m kwargs:\n\u001b[32m    184\u001b[39m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m185\u001b[39m         \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mconnect\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    186\u001b[39m     \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m:\n\u001b[32m    187\u001b[39m         \u001b[38;5;66;03m# Tidy-up underlying socket on failure\u001b[39;00m\n\u001b[32m    188\u001b[39m         \u001b[38;5;28mself\u001b[39m.close()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/uv_environments/testing/lib/python3.12/site-packages/mysql/connector/abstracts.py:1605\u001b[39m, in \u001b[36mMySQLConnectionAbstract.connect\u001b[39m\u001b[34m(self, **kwargs)\u001b[39m\n\u001b[32m   1602\u001b[39m     \u001b[38;5;28mself\u001b[39m.config(**kwargs)\n\u001b[32m   1604\u001b[39m \u001b[38;5;28mself\u001b[39m.disconnect()\n\u001b[32m-> \u001b[39m\u001b[32m1605\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_open_connection\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1607\u001b[39m charset, collation = (\n\u001b[32m   1608\u001b[39m     kwargs.pop(\u001b[33m\"\u001b[39m\u001b[33mcharset\u001b[39m\u001b[33m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m),\n\u001b[32m   1609\u001b[39m     kwargs.pop(\u001b[33m\"\u001b[39m\u001b[33mcollation\u001b[39m\u001b[33m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m),\n\u001b[32m   1610\u001b[39m )\n\u001b[32m   1611\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m charset \u001b[38;5;129;01mor\u001b[39;00m collation:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/uv_environments/testing/lib/python3.12/site-packages/mysql/connector/connection.py:382\u001b[39m, in \u001b[36mMySQLConnection._open_connection\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    380\u001b[39m \u001b[38;5;28mself\u001b[39m._socket = \u001b[38;5;28mself\u001b[39m._get_connection()\n\u001b[32m    381\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m382\u001b[39m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_socket\u001b[49m\u001b[43m.\u001b[49m\u001b[43mopen_connection\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    384\u001b[39m     \u001b[38;5;66;03m# do initial handshake\u001b[39;00m\n\u001b[32m    385\u001b[39m     \u001b[38;5;28mself\u001b[39m._do_handshake()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/uv_environments/testing/lib/python3.12/site-packages/mysql/connector/network.py:795\u001b[39m, in \u001b[36mMySQLTCPSocket.open_connection\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    793\u001b[39m     \u001b[38;5;28mself\u001b[39m.sock = socket.socket(\u001b[38;5;28mself\u001b[39m._family, socktype, proto)\n\u001b[32m    794\u001b[39m     \u001b[38;5;28mself\u001b[39m.sock.settimeout(\u001b[38;5;28mself\u001b[39m._connection_timeout)\n\u001b[32m--> \u001b[39m\u001b[32m795\u001b[39m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43msock\u001b[49m\u001b[43m.\u001b[49m\u001b[43mconnect\u001b[49m\u001b[43m(\u001b[49m\u001b[43msockaddr\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    796\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m (socket.timeout, \u001b[38;5;167;01mTimeoutError\u001b[39;00m) \u001b[38;5;28;01mas\u001b[39;00m err:\n\u001b[32m    797\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m ConnectionTimeoutError(\n\u001b[32m    798\u001b[39m         errno=\u001b[32m2003\u001b[39m,\n\u001b[32m    799\u001b[39m         values=(\n\u001b[32m   (...)\u001b[39m\u001b[32m    803\u001b[39m         ),\n\u001b[32m    804\u001b[39m     ) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01merr\u001b[39;00m\n", "\u001b[31mKeyboardInterrupt\u001b[39m: "]}], "source": ["# ----------------------- 6. Run Example Query -----------------------\n", "\n", "query = \"Find me a d with 2 years of experience\"\n", "\n", "print(f\"\\n🚀 Starting search for: '{query}'\\n\")\n", "\n", "initial_state = {\n", "    \"query\": query,\n", "    \"parsed\": None,\n", "    \"retries\": 0,\n", "    \"sql_query\": None,\n", "    \"sql_results\": None,\n", "    \"error\": None,\n", "}\n", "\n", "final_state = app.invoke(initial_state)\n", "\n", "print(\"\\n📦 Final State:\")\n", "print(json.dumps(final_state, indent=2, default=str))\n"]}, {"cell_type": "code", "execution_count": null, "id": "d8d3dd3a", "metadata": {}, "outputs": [], "source": ["from sqlalchemy import create_engine, text\n", "import urllib.parse\n", "\n", "# Load DB config from .env or use defaults\n", "db_host = os.getenv(\"MYSQL_HOST\")\n", "db_port = os.getenv(\"MYSQL_PORT\")\n", "db_user = os.getenv(\"MYSQL_USER\")\n", "db_password = os.getenv(\"MYSQL_PASSWORD\")\n", "db_name = os.getenv(\"CANDIDATE_DATABASE\") \n", "\n", "db_url = f\"mysql+mysqlconnector://{db_user}:{urllib.parse.quote_plus(str(db_password))}@{db_host}:{db_port}/{db_name}\"\n", "engine = create_engine(db_url)\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "4a111d22", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "testing", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}