import os
import sys
import json
import pandas as pd
import nest_asyncio
from typing import Any, Dict, List, Optional, TypedDict, Union
from dotenv import load_dotenv
from sqlalchemy import create_engine, text
from pydantic import BaseModel, Field
from langchain.output_parsers import PydanticOutputParser
from langchain.prompts import Chat<PERSON>romptTemplate
from langchain_openai import Chat<PERSON>penA<PERSON>
from langgraph.graph import END, StateGraph, START
from loguru import logger
from IPython.display import Image, display
from langchain_core.runnables.graph import MermaidDrawMethod


# ----------------------- 0. Environment Setup -----------------------
load_dotenv()
nest_asyncio.apply()

logger.remove()
logger.add(sys.stdout, format="<level>{level}</level>: <cyan>{message}</cyan>", colorize=True, level="DEBUG")
logger.level("DEBUG", color="<blue>")

# ----------------------- 1. Schema -----------------------

class LanguageItem(BaseModel):
    language: str = Field(..., description="The language spoken by the candidate")
    proficiency: Optional[str] = Field(None, description="Proficiency level")

class ParsedQuery(BaseModel):
    profession: Optional[str] = Field(None)
    # skills: Optional[List[str]] = Field(None)
    experience_years: Optional[Union[str, int]] = Field(None)
    education: Optional[str] = Field(None)
    languages: Optional[List[LanguageItem]] = Field(None)

class GraphState(TypedDict):
    query: str
    parsed: Optional[ParsedQuery]
    retries: int
    sql_query: Optional[str]
    sql_results: Optional[List[Dict[str, Any]]]
    error: Optional[str]

# ----------------------- 2. Database Config -----------------------
db_url = (
    f"mysql+pymysql://{os.getenv('MYSQL_USER')}:{os.getenv('MYSQL_PASSWORD')}"
    f"@{os.getenv('MYSQL_HOST')}:{os.getenv('MYSQL_PORT', 3306)}/candidate"
)
engine = create_engine(db_url)

# ----------------------- 3. LLM Setup -----------------------
api_key = os.getenv("OPENAI_API_KEY")
llm = ChatOpenAI(model="gpt-4.1-mini", temperature=0, api_key=api_key)

parser = PydanticOutputParser(pydantic_object=ParsedQuery)
format_instructions = parser.get_format_instructions()

prompt = ChatPromptTemplate.from_messages(
    [
        (
            "system",
            """You are a recruitment assistant. You will receive natural language queries from employers describing the kind of candidates they are looking for.

Your job is to carefully understand and analyze these queries and extract the following structured information:
- profession: the job title or role the employer is seeking
- skills: a list of specific skills or qualifications mentioned
- experience_years: number of years of experience required. If the query includes expressions like '5+ years', 'more than 3 years', or 'at least 2 years', capture the number and convert it to an integer.
- education: any educational requirements, such as 'bachelor's degree', 'MBA', etc.
- languages: any spoken or written languages mentioned, along with optional proficiency levels (e.g., B2, fluent)

You must ignore irrelevant details and focus only on the fields listed above. If a particular field is not mentioned, leave it as null.
Be strict and precise in your interpretation.

Output your response in the exact format described in the instructions below.
""",
        ),
        ("user", "{query}\n\n{format_instructions}"),
    ]
)

def parse_json_field(json_str: str) -> Optional[List[Dict[str, Any]]]:
    try:
        # Unescape and parse the JSON string
        return json.loads(json_str)
    except Exception as e:
        logger.error(f"Error parsing JSON field: {e}")
        return None

# ----------------------- 4. Graph Nodes -----------------------
def parse_query_node(state: GraphState) -> GraphState:
    query = state["query"]
    retries = state.get("retries", 0)

    try:
        formatted_prompt = prompt.format_messages(query=query, format_instructions=format_instructions)
        # logger.info(f"Formatted prompt: {formatted_prompt}\n\n")
        response = llm.invoke(formatted_prompt)
        # logger.info(f"LLM Response: {response.content}\n\n")
        parsed = parser.parse(response.content)
        logger.info(f"Parsed: {parsed}\n")

        print(f"\n🔍 Attempt {retries+1} | Parsed:\n{parsed}\n")

        if not any([parsed.profession, parsed.experience_years, parsed.education, parsed.languages]):
            return {**state, "parsed": None, "retries": retries + 1}

        return {**state, "parsed": parsed, "retries": retries}

    except Exception as e:
        print(f"❌ Error parsing: {e}")
        return {**state, "parsed": None, "error": str(e), "retries": retries + 1}

def build_sp_params(parsed: ParsedQuery) -> Dict[str, Any]:
    return {
        "uuid": "df2842bf-f831-47e1-9a75-242ecebcd21e",
        "keyword": parsed.profession or '',
        "years": int(parsed.experience_years or 0),
        "language": parsed.languages[0].language if parsed.languages else '',
        "proficiency": parsed.languages[0].proficiency if parsed.languages and parsed.languages[0].proficiency else '',
        "lang_code": 'en',
        "limit": 100
    }

def query_sp_node(state: GraphState) -> GraphState:
    parsed = state.get("parsed")
    # logger.info(f"Query SP Node: {parsed}\n")
    if not parsed:
        return state

    params = build_sp_params(parsed)
    logger.info(f"SP Params: {params}\n")

    try:
        with engine.connect() as conn:
            result = conn.execute(
                text("CALL sp_filter_candidate_for_sureai(:uuid, :keyword, :years, :language, :proficiency, :lang_code, :limit)"),
                params,
            )
            df = pd.DataFrame(result.fetchall(), columns=result.keys())
            return {**state, "sql_query": str(result), "sql_results": df.to_dict(orient="records")}
    except Exception as e:
        print(f"❌ SQL Error: {e}")
        return {**state, "error": str(e)}

# def result_node(state: GraphState) -> GraphState:
#     results = state.get("sql_results", [])
#     logger.info(f"Result Node: {results}\n")
#     # filtered_results = [{k: v for k, v in r.items() if k != "name"} for r in results]
#     # logger.info(f"Result Node (without name): {filtered_results}\n")

#     if not results:
#         print("⚠️ No results returned from SP.")
#     else:
#         print(f"✅ {len(results)} candidates found:")
#         df = pd.DataFrame(results)
#         print(df.head())
#     return state

def result_node(state: GraphState) -> GraphState:
    results = state.get("sql_results", [])
    logger.info(f"Result Node: {results}\n")

    if not results:
        print("⚠️ No results returned from SP.")
    else:
        print(f"✅ {len(results)} candidates found:")

        # Clean and convert JSON fields
        for r in results:
            for key in [
                "candidate_employment_details",
                "candidate_education_details",
                "candidate_certificate_details"
            ]:
                if key in r and isinstance(r[key], str):
                    r[key] = parse_json_field(r[key])

        # Now display or save cleaned result
        df = pd.DataFrame(results)
        print(df.head())

    return {**state, "sql_results": results}


def check_parsing_complete(state: GraphState) -> str:
    if state["parsed"] is not None:
        return "run_sp_query"
    elif state["retries"] >= 3:
        print("⚠️ Please search for a job title, skills, or relevant experience so I can assist you better!")
        return "none"
    else:
        return "retry"

workflow = StateGraph(GraphState)
workflow.add_node("parse_query", parse_query_node)
workflow.add_node("run_sp_query", query_sp_node)
workflow.add_node("show_results", result_node)

workflow.set_entry_point("parse_query")
workflow.add_edge(START, "parse_query")
workflow.add_conditional_edges(
    "parse_query",
    check_parsing_complete,
    {
        "run_sp_query": "run_sp_query",
        "retry": "parse_query",
        "none": END
    },
)
workflow.add_edge("run_sp_query", "show_results")
workflow.add_edge("show_results", END)

app = workflow.compile()

display(Image(app.get_graph().draw_mermaid_png(draw_method=MermaidDrawMethod.API)))

# ----------------------- 6. Run Example -----------------------

query = "Find me a nurse with 2 years of experience"

print(f"\n🚀 Starting search for: '{query}'\n")

initial_state = {
    "query": query,
    "parsed": None,
    "retries": 0,
    "sql_query": None,
    "sql_results": None,
    "error": None,
}

final_state = app.invoke(initial_state)

print("\n📦 Final State:")
# print(json.dumps(final_state, indent=2, default=str))

# ----------------------- 7. Save Results -----------------------

os.makedirs("matched_data", exist_ok=True)

with open("matched_data/matched_data.json", "w") as f:
    json.dump(final_state, f, indent=2, default=str, ensure_ascii=False)

sql_results = final_state.get("sql_results")
if sql_results:
    df = pd.DataFrame(sql_results)
    df.to_csv("matched_data/matched_data.csv", index=False)
    print("✅ Data saved to matched_data/matched_data.json and matched_data/matched_data.csv")
else:
    print("⚠️ No candidates to save.")


You need to act as an expert in understanding the current langchain and the langgraph code, about how it is working and how a new feature can be added to it using the prompt. The final answer that I am getting is through the SP which is been called through the database. In this response it will be having the Final output in the json form, I, and based on that you need to define the later steps that I will be telling to you. understood just say yes or no

Now I am having the prompt that I have written that is for the skills to extract, understand this end to end and than will tell u. 

You are an expert in understanding the content of the each Json data that you will be getting your task is to extract the skills out of the candidate_employment_details and from that you need to consider title + description and based on that when you learn for each of the rows data you need to provide me a skills that you think the candidate is having. Only you need to consider this only. rest of the data is not required. 



uuid                                |name             |profile_photo                                                                                         |country_name|city_name |state_name|total_experience_years|candidate_employment_details                                                                                                                                                                                                                                   |candidate_education_details                                                                                                                                                                                                                                    |candidate_certificate_details                                                                                                                                                                                                                                  |
------------------------------------+-----------------+------------------------------------------------------------------------------------------------------+------------+----------+----------+----------------------+---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
f62527b5-6ad1-4169-bede-c745700f92c3|Ernesta Cinfollas|                                                                                                      |            |Alcorcón  |          |                     1|[{"title": "Hospital nurse", "company_name": "Hospital for nursing students", "description": "yada yada", "start_date": "2021-10-01", "end_date": "2022-09-30", "employment_type": null}]                                                                      |[{"title": "Bachelor in Nursing", "description": "Finished my bachelor during the lockdowns", "institution": "UCM", "start_date": "2018-09-09", "end_date": "2021-06-15", "education_course_level": null}]                                                     |                                                                                                                                                                                                                                                               |
f455baca-233d-46d2-ad26-17dc4b917b37|Vipin K R        |candidates/f455baca-233d-46d2-ad26-17dc4b917b37/profile_photo/5a6b0fd4-4283-45b6-ac1b-4213928100e7.png|            |Coimbatore|          |                     5|[{"title": "Registered Nurse", "company_name": "Apollo Hospitals Coimbatore", "description": "Provided comprehensive nursing care to an average of 8-10 patients per shift in the Medical-Surgical and Critical Care Units.\n\nAdministered medications (oral, |[{"title": "Masters in Science in Nursing", "description": "Relevant coursework: Anatomy, Physiology, Pharmacology, Medical-Surgical Nursing, Pediatric Nursing, Community Health Nursing, Nutrition.", "institution": "Christian Medical College, Vellore, Ind|[{"title": "Registered Nurse (RN) License", "issued_by": "Maharashtra Nursing Council (MNC), India ", "date_of_issuance": "2021-08-10", "date_of_expiry": null, "content": ""},{"title": "Basic Life Support (BLS) Certified ", "issued_by": "Indian Resuscitat|


Step 1: Identify the entities from the query and extract them using the LLM

        - current entities that will be extracted from the query are: profession, skills, experience_years, language, proficiency.
        - If none of the above entities are found, then the query is invalid.
        - Note 1: SP is designed based on the above entites except the skill entity.
        - Note 2: If the skill is identified and extracted, then the extracted data will be sent to the LLM to extract the relevant 3 skills from the description of the candidate_employement_details.

Step 2: Once the entities are extracted, the SP will be called with the extracted entities as input parameters.

Step 3: If Skills are not identified than normal SP will be called with the extracted entities as input parameters. 

Step 4: If Skills are identified in the query than it will be sent to the LLM to extract the relevant 3 skills from the description of the candidate_employement_details and it will generate a Match Score out of 100. If description is not found or invalid than it will return 0.

Step 5: The result will be returned in the JSON format.

Normal SP query will run if there is no skill identified in the query.